# Facility Dashboard Metrics Calculations

## Overview
This document provides a comprehensive breakdown of all metrics calculations used in the facility dashboard, including the exact formulas, SQL queries, and business logic for each KPI.

## Core Metrics

### 1. Equipment Uptime Percentage
**Purpose**: Measures the percentage of equipment that is currently working (not having open work orders)

**Formula**:
```
Equipment Uptime % = (1 - (Assets with Open Work Orders / Total Assets)) × 100
```

**SQL Implementation**:
```sql
SELECT (1-(SUM(IF(w.woven_api_id is not null,1,0))/COUNT(a.woven_api_id)))*100 as UpPercentages
FROM woven_assets a
LEFT JOIN abc_club_settings s ON s.wovenid=a.location_id
LEFT JOIN abc_clubs c ON c.id=s.club_id
LEFT JOIN woven_workorders w ON a.woven_api_id=w.asset_id 
    AND work_order_status<>'2'  -- NOT closed
    AND w.is_deleted=0          -- NOT deleted
WHERE asset_type='1'            -- Equipment only (not facility)
GROUP BY s.wovenid
```

**Business Logic**:
- Only counts equipment assets (`asset_type='1'`)
- Excludes closed work orders (`work_order_status<>'2'`)
- Excludes deleted work orders (`is_deleted=0`)
- Higher percentage = better performance
- Target: 99%+ (configurable via `FacEqupWorking` setting)

**Color Coding**:
- **Green** (`#00ff27`): >= `FacEqupWorking` threshold (default 95%)
- **Yellow** (`#ffff00`): >= `FacEqupWorkingMid` threshold (default 85%)
- **Red** (`#ff4136`): < `FacEqupWorkingMid` threshold

---

### 2. Open Work Orders Count
**Purpose**: Total number of unresolved work orders (both asset and facility)

**Formula**:
```
Open Work Orders = COUNT(work_orders WHERE status != 'Closed' AND not deleted)
```

**SQL Implementation**:
```sql
SELECT c.ClubNumber, 
       SUM(IF(work_order_type='1',1,0)) AS AssetWO,
       SUM(IF(work_order_type='2',1,0)) AS FacilityWO,
       COUNT(work_order_number) AS TotalWO
FROM woven_workorders w
LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
LEFT JOIN abc_clubs c ON c.id=s.club_id
WHERE work_order_status<>'2' AND w.is_deleted=0
GROUP BY c.id
```

**Business Logic**:
- Includes both asset (`work_order_type='1'`) and facility (`work_order_type='2'`) work orders
- Excludes closed work orders (`work_order_status<>'2'`)
- Excludes deleted work orders (`is_deleted=0`)
- Lower count = better performance
- Target: Less than 5 (configurable via `FacOpenWO` setting)

**Color Coding**:
- **Green** (`#00ff27`): <= `FacOpenWO` threshold (default 5)
- **Yellow** (`#ffff00`): <= `FacOpenWOMid` threshold (default 10)
- **Red** (`#ff4136`): > `FacOpenWOMid` threshold

---

### 3. Average Days Open (Aging)
**Purpose**: Average time that open work orders have been unresolved

**Formula**:
```
Average Days Open = AVG(TIMESTAMPDIFF(SECOND, created_on, NOW())) / 86400
```

**SQL Implementation**:
```sql
SELECT AVG(timestampdiff(SECOND, created_on, NOW())) AS AVGTimeDiff,
       CONCAT(
           FLOOR(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H') / 24), ' day(s), ',
           MOD(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H'), 24), ' hour(s), ',
           TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%i'), ' min(s)'
       ) AS ConvertTime
FROM woven_workorders w
LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
LEFT JOIN abc_clubs c ON c.id=s.club_id
WHERE work_order_status<>'2' AND w.is_deleted=0
GROUP BY c.id
```

**Business Logic**:
- Calculates time difference from `created_on` to current time
- Only includes open work orders (`work_order_status<>'2'`)
- Excludes deleted work orders (`is_deleted=0`)
- Converts seconds to human-readable format (days, hours, minutes)
- Lower average = better performance
- Target: Less than 5 days (configurable via `FacAging` setting)

**Color Coding**:
- **Green** (`#00ff27`): <= `FacAging` threshold (default 3 days)
- **Yellow** (`#ffff00`): <= `FacAgingMid` threshold (default 7 days)
- **Red** (`#ff4136`): > `FacAgingMid` threshold

---

### 4. Work Orders Opened Today
**Purpose**: Number of new work orders created today

**Formula**:
```
Opened Today = COUNT(work_orders WHERE DATE(created_on) = CURDATE())
```

**SQL Implementation**:
```sql
SELECT c.ClubNumber,
       SUM(IF(work_order_type='1',1,0)) AS AssetWO,
       SUM(IF(work_order_type='2',1,0)) AS FacilityWO,
       COUNT(work_order_number) AS TotalWO
FROM woven_workorders w
LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
LEFT JOIN abc_clubs c ON c.id=s.club_id
WHERE Date(created_on) = CURDATE()
GROUP BY c.id
```

**Business Logic**:
- Includes all work orders created today (regardless of status)
- Separates asset vs facility work orders
- Used for trend analysis and daily monitoring

---

### 5. Work Orders Closed Today
**Purpose**: Number of work orders resolved today

**Formula**:
```
Closed Today = COUNT(work_orders WHERE DATE(closed_date) = CURDATE() AND status = 'Closed')
```

**SQL Implementation**:
```sql
SELECT c.ClubNumber,
       SUM(IF(work_order_type='1',1,0)) AS AssetWO,
       SUM(IF(work_order_type='2',1,0)) AS FacilityWO,
       COUNT(work_order_number) AS TotalWO
FROM woven_workorders w
LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
LEFT JOIN abc_clubs c ON c.id=s.club_id
WHERE work_order_status='2' AND Date(closed_date) = CURDATE()
GROUP BY c.id
```

**Business Logic**:
- Only includes closed work orders (`work_order_status='2'`)
- Must have been closed today (`Date(closed_date) = CURDATE()`)
- Used for productivity tracking and trend analysis

---

### 6. Work Orders Opened Yesterday
**Purpose**: Number of new work orders created yesterday (for comparison)

**Formula**:
```
Opened Yesterday = COUNT(work_orders WHERE DATE(created_on) = CURDATE() - 1)
```

**SQL Implementation**:
```sql
SELECT c.ClubNumber,
       SUM(IF(work_order_type='1',1,0)) AS AssetWO,
       SUM(IF(work_order_type='2',1,0)) AS FacilityWO,
       COUNT(work_order_number) AS TotalWO
FROM woven_workorders w
LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
LEFT JOIN abc_clubs c ON c.id=s.club_id
WHERE Date(created_on) = SUBDATE(CURRENT_DATE, 1)
GROUP BY c.id
```

---

### 7. Work Orders Closed Yesterday
**Purpose**: Number of work orders resolved yesterday (for comparison)

**Formula**:
```
Closed Yesterday = COUNT(work_orders WHERE DATE(closed_date) = CURDATE() - 1 AND status = 'Closed')
```

**SQL Implementation**:
```sql
SELECT c.ClubNumber,
       SUM(IF(work_order_type='1',1,0)) AS AssetWO,
       SUM(IF(work_order_type='2',1,0)) AS FacilityWO,
       COUNT(work_order_number) AS TotalWO
FROM woven_workorders w
LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
LEFT JOIN abc_clubs c ON c.id=s.club_id
WHERE work_order_status='2' AND Date(closed_date) = SUBDATE(CURRENT_DATE, 1)
GROUP BY c.id
```

## Aggregation and Totals

### District-Level Aggregation
**Purpose**: Group clubs by district and calculate district averages

**Logic**:
- Groups clubs by `abc_club_settings.MaintainDistrict` field
- Calculates averages for uptime percentage and aging
- Sums total assets and work orders
- Applies same color coding logic to district averages

### Company-Wide Totals
**Purpose**: Calculate overall company performance across all clubs

**Calculations**:
- **Total Equipment Uptime**: Average of all club uptime percentages
- **Total Open Work Orders**: Sum of all open work orders across clubs
- **Average Days Open**: Average aging across all clubs
- **Daily Totals**: Sum of daily opened/closed work orders

## Configuration Settings

### Threshold Settings (stored in `abc_company_settings`)

| Setting Key | Default Value | Description |
|-------------|---------------|-------------|
| `FacEqupWorking` | 0.95 | Equipment uptime threshold (high) - 95% |
| `FacEqupWorkingMid` | 0.85 | Equipment uptime threshold (medium) - 85% |
| `FacOpenWO` | 5 | Open work orders threshold (low is good) - 5 or fewer |
| `FacOpenWOMid` | 10 | Open work orders threshold (medium) - 10 or fewer |
| `FacAging` | 3 | Work order aging threshold in days (low is good) - 3 days |
| `FacAgingMid` | 7 | Work order aging threshold in days (medium) - 7 days |

### Color Coding System
- **Green** (`#00ff27`): Good performance (meets high threshold)
- **Yellow** (`#ffff00`): Warning (meets medium threshold)
- **Red** (`#ff4136`): Critical (below medium threshold)
- **White**: No data or configuration missing

## Data Sources

### Primary Tables
- **`woven_assets`**: Equipment and facility assets
- **`woven_workorders`**: Maintenance work orders
- **`abc_clubs`**: Club master data
- **`abc_club_settings`**: Club configuration and Woven location mapping
- **`abc_company_settings`**: Performance thresholds

### Key Relationships
- `woven_assets.location_id` → `abc_club_settings.wovenid`
- `woven_workorders.location_id` → `abc_club_settings.wovenid`
- `woven_workorders.asset_id` → `woven_assets.woven_api_id`
- `abc_club_settings.club_id` → `abc_clubs.id`

## Performance Considerations

### Database Indexes
Critical indexes for optimal performance:
```sql
-- Asset queries
CREATE INDEX idx_woven_assets_location_type ON woven_assets(location_id, asset_type);

-- Work order queries
CREATE INDEX idx_woven_workorders_status_deleted ON woven_workorders(work_order_status, is_deleted);
CREATE INDEX idx_woven_workorders_location_status ON woven_workorders(location_id, work_order_status);
CREATE INDEX idx_woven_workorders_created_on ON woven_workorders(created_on);
CREATE INDEX idx_woven_workorders_closed_date ON woven_workorders(closed_date);

-- Club settings
CREATE INDEX idx_abc_club_settings_wovenid ON abc_club_settings(wovenid);
```

### Caching Strategy
- Dashboard data cached for 5 minutes
- Company settings cached for 1 hour
- Last sync time cached for 1 minute

This comprehensive metrics documentation ensures accurate implementation and maintenance of the facility dashboard calculations in both the current CodeIgniter system and the future Laravel V2 migration.

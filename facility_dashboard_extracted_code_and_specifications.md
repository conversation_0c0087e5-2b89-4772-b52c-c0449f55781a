# Facility Dashboard - Extracted Code and Specifications

## 1. Extracted Metrics Calculation Code

### A. Equipment Uptime Calculation

#### Method: `get_equipment_up($from, $to, $club_id)`
```php
function get_equipment_up($from, $to, $club_id) {
    $CI = & get_instance();
    
    $query = "SELECT c.ClubNumber, GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                    s.MaintainDistrict, IFNULL(Assets,0) AS Assets,
                    IFNULL(WorkOrders,0) AS WorkOrders, IFNULL(UpPercentages,0) AS UpPercentages
                FROM abc_clubs c
                LEFT JOIN (SELECT c.ClubNumber,
                    GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                    MaintainDistrict,
                    COUNT(a.woven_api_id) AS Assets,
                    SUM(IF(w.woven_api_id is not null,1,0)) AS WorkOrders,
                    (1-(SUM(IF(w.woven_api_id is not null,1,0))/COUNT(a.woven_api_id)))*100 as UpPercentages
                FROM woven_assets a
                LEFT JOIN abc_club_settings s ON s.wovenid=a.location_id
                LEFT JOIN abc_clubs c ON c.id=s.club_id
                LEFT JOIN woven_workorders w ON a.woven_api_id=w.asset_id AND work_order_status<>'2' AND w.is_deleted=0
                WHERE asset_type='1' ";
    $query .= is_numeric($club_id) ? " AND c.id = {$club_id} " : " ";
    $query .= " GROUP BY s.wovenid
                ORDER BY MaintainDistrict, 1-(SUM(IF(w.woven_api_id is not null,1,0))/COUNT(a.woven_api_id)) DESC)
            AS a ON a.ClubNumber=c.ClubNumber
            LEFT JOIN abc_club_settings s ON s.club_id=c.id
            WHERE GrandOpening <= CURDATE()
            GROUP BY c.ClubNumber
            ORDER BY MaintainDistrict";
    
    $result = $CI->db->query($query)->result_array();
    return array_column($result, NULL, 'ClubNumber');
}
```

#### Total Equipment Uptime: `get_equipment_up_total_results()`
```php
function get_equipment_up_total_results() {
    $CI = & get_instance();
    
    $query = "SELECT (1-(SUM(IF(w.woven_api_id is not null,1,0))/COUNT(a.woven_api_id)))*100 as UpPercentages
                FROM woven_assets a
                LEFT JOIN abc_club_settings s ON s.wovenid=a.location_id
                LEFT JOIN abc_clubs c ON c.id=s.club_id
                LEFT JOIN woven_workorders w ON a.woven_api_id=w.asset_id AND work_order_status<>'2' AND w.is_deleted=0
                WHERE asset_type='1'";
    
    $result = $CI->db->query($query)->result_array();
    $result = reset($result);
    $result = isset($result['UpPercentages']) ? $result['UpPercentages'] : 0;
    
    return $result;
}
```

### B. Open Work Orders Count

#### Method: `get_open_wo($club_id)`
```php
function get_open_wo($club_id) {
    $CI = & get_instance();
    
    $query = "SELECT c.ClubNumber,
                    GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                    SUM(IF(work_order_type='1',1,0)) AS AssetWO,
                    SUM(IF(work_order_type='2',1,0)) AS FacilityWO,
                    COUNT(work_order_number) AS TotalWO
                FROM woven_workorders w
                LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
                LEFT JOIN abc_clubs c ON c.id=s.club_id
                WHERE work_order_status<>'2' AND w.is_deleted=0 AND GrandOpening <= CURDATE()";
    $query .= is_numeric($club_id) ? " AND c.id = {$club_id} " : " ";
    $query .= " GROUP BY c.id";
    
    $result = $CI->db->query($query)->result_array();
    return array_column($result, NULL, 'ClubNumber');
}
```

### C. Work Order Aging (Average Days Open)

#### Method: `get_workorder_opentime($club_id)`
```php
function get_workorder_opentime($club_id) {
    $CI = & get_instance();
    
    $query = "SELECT ClubNumber, name,
                    CONCAT(
                        FLOOR(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H') / 24), ' day(s),  ',
                        MOD(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H'), 24), ' hour(s), ',
                        TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%i'), ' min(s)') AS AvgOpen,
                        FLOOR(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H') / 24) AS DaysOpen,
                        AVGTimeDiff
            FROM(
            SELECT c.ClubNumber,
                            GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                            AVG(timestampdiff(SECOND, created_on, NOW())) AS AVGTimeDiff
            FROM woven_workorders w
            LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
            LEFT JOIN abc_clubs c ON c.id=s.club_id
            WHERE work_order_status<>'2' AND w.is_deleted=0
            AND GrandOpening <= CURDATE()
            GROUP BY c.id) AS t";
    
    $result = $CI->db->query($query)->result_array();
    return array_column($result, NULL, 'ClubNumber');
}
```

#### Total Average Days Open: `get_workorder_opentime_total_result()`
```php
function get_workorder_opentime_total_result() {
    $CI = & get_instance();
    
    $query = "SELECT CONCAT(
                        FLOOR(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H') / 24), ' day(s),  ',
                        MOD(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H'), 24), ' hour(s), ',
                        TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%i'), ' min(s)') AS AvgOpen
            FROM(
            SELECT AVG(timestampdiff(SECOND, created_on, NOW())) AS AVGTimeDiff
            FROM woven_workorders w
            LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
            LEFT JOIN abc_clubs c ON c.id=s.club_id
            WHERE work_order_status<>'2' AND w.is_deleted=0) AS t";
    
    $result = $CI->db->query($query)->result_array();
    $result = reset($result);
    $result = isset($result['AvgOpen']) ? $result['AvgOpen'] : NULL;
    
    return $result;
}
```

### D. Daily Work Order Counts

#### Work Orders Opened Today: `get_open_wo_today($club_id)`
```php
function get_open_wo_today($club_id) {
    $CI = & get_instance();
    
    $query = "SELECT c.ClubNumber,
                    GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                    COUNT(work_order_number) AS OpenToday
                FROM woven_workorders w
                LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
                LEFT JOIN abc_clubs c ON c.id=s.club_id
                WHERE DATE(created_on)=curdate() AND GrandOpening <= CURDATE()
                AND is_deleted=0";
    $query .= is_numeric($club_id) ? " AND c.id = {$club_id} " : " ";
    $query .= " GROUP BY c.id";
    
    $result = $CI->db->query($query)->result_array();
    return array_column($result, NULL, 'ClubNumber');
}
```

#### Work Orders Closed Today: `get_closed_wo_today($club_id)`
```php
function get_closed_wo_today($club_id) {
    $CI = & get_instance();
    
    $query = "SELECT c.ClubNumber,
                    GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                    COUNT(work_order_number) AS ClosedToday
                FROM woven_workorders w
                LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
                LEFT JOIN abc_clubs c ON c.id=s.club_id
                WHERE work_order_status='2' AND w.is_deleted=0 AND GrandOpening <= CURDATE()
                AND DATE(closed_date)=curdate()";
    $query .= is_numeric($club_id) ? " AND c.id = {$club_id} " : " ";
    $query .= " GROUP BY c.id";
    
    $result = $CI->db->query($query)->result_array();
    return array_column($result, NULL, 'ClubNumber');
}
```

#### Work Orders Opened Yesterday: `get_open_wo_yesterday($club_id)`
```php
function get_open_wo_yesterday($club_id) {
    $CI = & get_instance();
    
    $query = "SELECT c.ClubNumber,
                    GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                    COUNT(work_order_number) AS OpenYesterday
                FROM woven_workorders w
                LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
                LEFT JOIN abc_clubs c ON c.id=s.club_id
                WHERE DATE(created_on)=DATE_ADD(curdate(), INTERVAL -1 DAY)
                AND is_deleted=0 AND GrandOpening <= CURDATE()";
    $query .= is_numeric($club_id) ? " AND c.id = {$club_id} " : " ";
    $query .= " GROUP BY c.id";
    
    $result = $CI->db->query($query)->result_array();
    return array_column($result, NULL, 'ClubNumber');
}
```

#### Work Orders Closed Yesterday: `get_closed_wo_yesterday($club_id)`
```php
function get_closed_wo_yesterday($club_id) {
    $CI = & get_instance();
    
    $query = "SELECT c.ClubNumber,
                GROUP_CONCAT(DISTINCT c.name ORDER BY c.name ASC SEPARATOR ', ') as name,
                    COUNT(work_order_number) AS ClosedYesterday
                FROM woven_workorders w
                LEFT JOIN abc_club_settings s ON s.wovenid=w.location_id
                LEFT JOIN abc_clubs c ON c.id=s.club_id
                WHERE work_order_status='2' AND w.is_deleted=0
                AND DATE(closed_date)=curdate()-1  AND GrandOpening <= CURDATE()";
    $query .= is_numeric($club_id) ? " AND c.id = {$club_id} " : " ";
    $query .= " GROUP BY c.id";
    
    $result = $CI->db->query($query)->result_array();
    return array_column($result, NULL, 'ClubNumber');
}
```

## 2. Specific Formulas and Business Logic

### A. Core Calculation Formulas

#### Equipment Uptime Percentage
```sql
-- Formula: (1 - (Assets with Open Work Orders / Total Assets)) × 100
(1-(SUM(IF(w.woven_api_id is not null,1,0))/COUNT(a.woven_api_id)))*100 as UpPercentages
```

#### Average Days Open (Aging)
```sql
-- Formula: Average seconds since creation converted to days
AVG(timestampdiff(SECOND, created_on, NOW())) AS AVGTimeDiff

-- Human-readable format conversion:
CONCAT(
    FLOOR(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H') / 24), ' day(s),  ',
    MOD(TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%H'), 24), ' hour(s), ',
    TIME_FORMAT(SEC_TO_TIME(AVGTimeDiff), '%i'), ' min(s)'
) AS AvgOpen
```

#### District-Level Aggregation (from view)
```php
// District uptime calculation
if(isset($numAssets) && $numAssets>0) {
    $upPercent = (1-($numWO/$numAssets))*100;
} else {
    $upPercent = 0;
}

// District averages
$openWO/$count  // Average open work orders per club in district
ROUND(($avgTimeDiff/$count)/86400)  // Average days open in district (convert seconds to days)
```

### B. Work Order Status Logic

#### Open Work Orders Filter
```sql
-- Work orders are considered "open" when:
work_order_status<>'2' AND w.is_deleted=0

-- Status meanings:
-- '2' = Closed
-- All other statuses = Open (Draft, In Progress, etc.)
```

#### Asset Type Classification
```sql
-- Asset types:
asset_type='1'  -- Equipment (used for uptime calculation)
asset_type='2'  -- Facility

-- Work order types:
work_order_type='1'  -- Asset work orders
work_order_type='2'  -- Facility work orders
```

### C. Date Range Logic

#### Today's Work Orders
```sql
DATE(created_on)=curdate()        -- Opened today
DATE(closed_date)=curdate()       -- Closed today
```

#### Yesterday's Work Orders
```sql
DATE(created_on)=DATE_ADD(curdate(), INTERVAL -1 DAY)  -- Opened yesterday
DATE(closed_date)=curdate()-1                         -- Closed yesterday
```

#### Club Filtering
```sql
-- Only include clubs that have opened:
GrandOpening <= CURDATE()

-- Club-specific filtering:
is_numeric($club_id) ? " AND c.id = {$club_id} " : " "
```

## 3. Color Coding Functions

### A. Equipment Working Color Function
```php
function getFacEqupWorkingColor($equWorkingValue, $companySettings) {
    $equWorkingValue = floatval($equWorkingValue);
    $facColors = array(
        '#ff4136',  // Red
        '#ffff00',  // Yellow
        '#00ff27'   // Green
    );

    if (empty($companySettings) || !isset($companySettings['FacEqupWorking']) || !isset($companySettings['FacEqupWorkingMid'])) {
        return 'white';
    }

    if ($equWorkingValue >= $companySettings['FacEqupWorking']*100) {
        return $facColors[2];  // Green
    } elseif ($equWorkingValue >= $companySettings['FacEqupWorkingMid']*100) {
        return $facColors[1];  // Yellow
    } else {
        return $facColors[0];  // Red
    }
}
```

### B. Open Work Orders Color Function
```php
function getFacOpenWO($open_woValue, $companySettings) {
    $open_woValue = floatval($open_woValue);
    $facColors = array(
        '#ff4136',  // Red
        '#ffff00',  // Yellow
        '#00ff27'   // Green
    );

    if (empty($companySettings) || !isset($companySettings['FacOpenWO']) || !isset($companySettings['FacOpenWOMid'])) {
        return 'white';
    }

    if ($open_woValue <= $companySettings['FacOpenWO']) {
        return $facColors[2];  // Green
    } elseif ($open_woValue <= $companySettings['FacOpenWOMid']) {
        return $facColors[1];  // Yellow
    } else {
        return $facColors[0];  // Red
    }
}
```

### C. Days Open (Aging) Color Function
```php
function getFacDaysOpen($open_Days, $companySettings) {
    $open_Days = floatval($open_Days);
    $facColors = array(
        '#ff4136',  // Red
        '#ffff00',  // Yellow
        '#00ff27'   // Green
    );

    if (empty($companySettings) || !isset($companySettings['FacAging']) || !isset($companySettings['FacAgingMid'])) {
        return 'white';
    }

    if ($open_Days <= $companySettings['FacAging']) {
        return $facColors[2];  // Green
    } elseif ($open_Days <= $companySettings['FacAgingMid']) {
        return $facColors[1];  // Yellow
    } else {
        return $facColors[0];  // Red
    }
}
```

## 4. Controller Implementation

### A. Main Controller Method: `facility()`
```php
public function facility()
{
    $club = $this->input->get('club', TRUE) ?? 'all';
    $from = $this->input->get('from', TRUE) ?? date('Y-m-d').' 00:00:00';
    $to = $this->input->get('to', TRUE) ?? date('Y-m-d').' 23:59:59';

    $companySettings = $this->dashboard_model->getCompanySettings();

    // Get all metrics data
    $equipment_up = $this->dashboard_model->get_equipment_up($from, $to, $club);
    $open_time = $this->dashboard_model->get_workorder_opentime($club);
    $open_wo = $this->dashboard_model->get_open_wo($club);
    $open_today = $this->dashboard_model->get_open_wo_today($club);
    $open_yesterday = $this->dashboard_model->get_open_wo_yesterday($club);
    $closed_today = $this->dashboard_model->get_closed_wo_today($club);
    $closed_yesterday = $this->dashboard_model->get_closed_wo_yesterday($club);

    $data = array();
    $total = array();

    // Process equipment uptime data
    $club_count=0;
    foreach ($equipment_up as $storeId => $store) {
        store_name_populate($data, $storeId, $store['name']);

        $equipment_upValue = $store['UpPercentages'];
        $equipment_upColor = getFacEqupWorkingColor($equipment_upValue, $companySettings);

        $data[$storeId]['UpPercentages'] = array('value' => $equipment_upValue, 'color' => $equipment_upColor);
        $club_count+=1;
    }

    // Process work order aging data
    foreach ($open_time as $storeId => $store) {
        store_name_populate($data, $storeId, $store['name']);

        $open_timeValue = $store['AvgOpen'];
        $open_Days = $store['DaysOpen'];
        $open_timeValueColor = getFacDaysOpen($open_Days, $companySettings);

        $data[$storeId]['AvgOpen'] = array('value' => $open_timeValue, 'color' => $open_timeValueColor);
    }

    // Process open work orders data
    $total["TotalWO"]['value'] = 0;
    foreach ($open_wo as $storeId => $store) {
        store_name_populate($data, $storeId, $store['name']);

        $open_woValue = $store['TotalWO'];
        $open_woValueColor = getFacOpenWO($open_woValue, $companySettings);

        $data[$storeId]['TotalWO'] = array('value' => $open_woValue, 'color' => $open_woValueColor);
        $total["TotalWO"]['value'] += $store['TotalWO'];
    }

    // Process daily work order counts (no color coding)
    $total["OpenToday"]['value'] = 0;
    foreach ($open_today as $storeId => $store) {
        store_name_populate($data, $storeId, $store['name']);
        $open_woToday = $store['OpenToday'];
        $data[$storeId]['OpenToday'] = array('value' => $open_woToday, 'color' => '');
        $total["OpenToday"]['value'] += $store['OpenToday'];
    }

    $total["ClosedToday"]['value'] = 0;
    foreach ($closed_today as $storeId => $store) {
        store_name_populate($data, $storeId, $store['name']);
        $closed_woToday = $store['ClosedToday'];
        $data[$storeId]['ClosedToday'] = array('value' => $closed_woToday, 'color' => '');
        $total["ClosedToday"]['value'] += $store['ClosedToday'];
    }

    $total["OpenYesterday"]['value'] = 0;
    foreach ($open_yesterday as $storeId => $store) {
        store_name_populate($data, $storeId, $store['name']);
        $open_woYesterday = $store['OpenYesterday'];
        $data[$storeId]['OpenYesterday'] = array('value' => $open_woYesterday, 'color' => '');
        $total["OpenYesterday"]['value'] += $store['OpenYesterday'];
    }

    $total["ClosedYesterday"]['value'] = 0;
    foreach ($closed_yesterday as $storeId => $store) {
        store_name_populate($data, $storeId, $store['name']);
        $closed_woYesterday = $store['ClosedYesterday'];
        $data[$storeId]['ClosedYesterday'] = array('value' => $closed_woYesterday, 'color' => '');
        $total["ClosedYesterday"]['value'] += $store['ClosedYesterday'];
    }

    // Calculate totals (averages)
    if($club_count>0) {
        $total["TotalWO"]['value'] = ROUND($total["TotalWO"]['value']/$club_count,1);
        $total["OpenToday"]['value'] = ROUND($total["OpenToday"]['value']/$club_count,1);
        $total["ClosedToday"]['value'] = ROUND($total["ClosedToday"]['value']/$club_count,1);
        $total["ClosedYesterday"]['value'] = ROUND($total["ClosedYesterday"]['value']/$club_count,1);
        $total["OpenYesterday"]['value'] = ROUND($total["OpenYesterday"]['value']/$club_count,1);
    }

    // Get company-wide totals
    $total["UpPercentages"]['value'] = number_format($this->dashboard_model->get_equipment_up_total_results(), 2);
    $total["AvgOpen"]['value'] = $this->dashboard_model->get_workorder_opentime_total_result();

    // Prepare view data
    $this->data['club_id'] = $club;
    $this->data['companySettings'] = $companySettings;
    $this->data['data'] = $data;
    $this->data['total'] = $total;
    $this->data['last_sync_date'] = $this->abcfinancial_syncs_model->getLastSuccessfulProcessingMembers($club == 'all' ? 1 : $club);

    $this->template->admin_render('admin/dashboard/facility', $this->data);
}
```

### B. Helper Function: `store_name_populate()`
```php
function store_name_populate(&$data, $clubNumber, $name) {
    if (!isset($data[$clubNumber]['name'])) {
        $data[$clubNumber]['name'] = array('value' => $name, 'color' => '');
    }
}
```

## 5. Data Sources and Table Structures

### A. Primary Tables

#### `woven_assets` Table
```sql
CREATE TABLE `woven_assets` (
  `woven_api_id` varchar(36) NOT NULL,
  `location_id` varchar(36) NOT NULL,
  `asset_type` tinyint(2) DEFAULT NULL,
  `asset_tag_number` varchar(50) DEFAULT NULL,
  `serial_number` varchar(50) DEFAULT NULL,
  `date_of_purchase` datetime DEFAULT NULL,
  `date_of_install` datetime DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT NULL,
  `location_name` varchar(100) DEFAULT NULL,
  `asset_category` varchar(100) DEFAULT NULL,
  `asset_subcategory` varchar(100) DEFAULT NULL,
  `manufacturer` varchar(100) DEFAULT NULL,
  `manufacturer_model` varchar(100) DEFAULT NULL,
  `manufacturer_style` varchar(200) DEFAULT NULL,
  `metered_name` varchar(200) DEFAULT NULL,
  `metered_value` smallint(5) DEFAULT NULL,
  `last_metered_usage_date` datetime DEFAULT NULL,
  `license_plate` varchar(200) DEFAULT NULL,
  `vehicle_driver` varchar(200) DEFAULT NULL,
  `room_number` smallint(6) DEFAULT NULL,
  `display_string_long` text,
  `hvac_type` tinyint(1) DEFAULT NULL,
  `hvac_sub_type` tinyint(1) DEFAULT NULL,
  `hvac_heat_fuel_type` tinyint(1) DEFAULT NULL,
  `hvac_size` tinyint(1) DEFAULT NULL,
  `hvac_crtu_number` tinyint(1) DEFAULT NULL,
  `hvac_manufactured_date` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

#### `woven_workorders` Table
```sql
CREATE TABLE `woven_workorders` (
  `asset_id` varchar(36) NOT NULL,
  `assigned_employee_id` varchar(36) DEFAULT NULL,
  `closed_by_employee_id` varchar(36) DEFAULT NULL,
  `note_last_added_or_changed_on` datetime DEFAULT NULL,
  `is_last_note_recent` tinyint(1) DEFAULT NULL,
  `asset_category_prompt_to_log_time` tinyint(1) DEFAULT NULL,
  `work_order_reason_list_color` varchar(100) DEFAULT NULL,
  `woven_api_id` varchar(36) NOT NULL,
  `company_id` varchar(36) DEFAULT NULL,
  `location_id` varchar(36) DEFAULT NULL,
  `location_name` varchar(100) DEFAULT NULL,
  `location_number` varchar(100) DEFAULT NULL,
  `work_order_number` int(10) DEFAULT NULL,
  `work_order_type` smallint(4) DEFAULT NULL,
  `work_order_type_display` varchar(100) DEFAULT NULL,
  `work_order_status` smallint(4) DEFAULT NULL,
  `work_order_status_display` varchar(100) DEFAULT NULL,
  `work_order_priority` smallint(4) DEFAULT NULL,
  `work_order_priority_display` varchar(100) DEFAULT NULL,
  `work_order_priority_list_color` varchar(100) DEFAULT NULL,
  `work_order_parts_status` tinyint(2) DEFAULT NULL,
  `work_order_parts_status_display` varchar(100) DEFAULT NULL,
  `work_order_reason_id` varchar(36) DEFAULT NULL,
  `work_order_reason` varchar(100) DEFAULT NULL,
  `facility_trade_id` varchar(36) DEFAULT NULL,
  `facility_trade` varchar(100) DEFAULT NULL,
  `asset_tag_number` varchar(100) DEFAULT NULL,
  `assigned_employee_name` varchar(100) DEFAULT NULL,
  `closed_by_employee_name` varchar(100) DEFAULT NULL,
  `location_physical_areas_display` varchar(200) DEFAULT NULL,
  `hours_logged` smallint(5) DEFAULT NULL,
  `closed_date` datetime DEFAULT NULL,
  `preventative_maintenance_id` varchar(36) DEFAULT NULL,
  `is_external_sourced` tinyint(1) DEFAULT NULL,
  `is_warranty_work` tinyint(1) DEFAULT NULL,
  `is_automated` tinyint(1) DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  KEY `asset_id` (`asset_id`),
  KEY `work_order_status` (`work_order_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

#### `abc_club_settings` Table
```sql
CREATE TABLE `abc_club_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `club_id` int(11) NOT NULL,
  `SalesTax` double(5,5) DEFAULT NULL,
  `AvgDuesGoal` double(5,2) DEFAULT NULL,
  `AvgDuesMid` double(5,2) DEFAULT NULL,
  `PeakPerGoal` double(5,2) DEFAULT NULL,
  `CheckGoal` double(10,2) DEFAULT NULL,
  `EFTGoal` smallint(6) DEFAULT NULL,
  `abc_late_fee_include` smallint(6) DEFAULT '0',
  `LeadGenStart` date DEFAULT NULL,
  `FirstMemSales` date DEFAULT NULL,
  `PreSaleStart` date DEFAULT NULL,
  `InPreSale` date DEFAULT NULL,
  `GrandOpening` date DEFAULT NULL,
  `Open_Gym_Bonus_Date` date DEFAULT NULL,
  `FlexEffectiveDate` date DEFAULT NULL,
  `sunlync_storecode` varchar(10) DEFAULT NULL,
  `paycom_location` varchar(255) DEFAULT NULL,
  `TruAssetID` varchar(50) DEFAULT NULL,
  `cms_clubname` varchar(50) DEFAULT NULL,
  `ukg_clubname` varchar(50) DEFAULT NULL,
  `WovenID` varchar(36) DEFAULT NULL,
  `Region` varchar(100) DEFAULT NULL,
  `Region_long` varchar(200) DEFAULT NULL,
  `RegionID` int(11) DEFAULT NULL,
  `RegionalVPId` smallint(6) DEFAULT NULL,
  `PTRegion` varchar(100) DEFAULT NULL,
  `MaintainDistrict` varchar(50) DEFAULT NULL,
  `GFI_Payroll` varchar(10) DEFAULT NULL,
  `PT_Payroll` varchar(10) DEFAULT NULL,
  `base_term_pricing` double(10,2) DEFAULT '9.99',
  `base_mtm_pricing` double(10,2) DEFAULT '15.99',
  `peak_pricing` double(10,2) DEFAULT '24.99',
  `peak_results_pricing` double(10,2) DEFAULT '29.99',
  PRIMARY KEY (`club_id`,`id`),
  KEY `id` (`id`),
  KEY `sunlync_storecode` (`sunlync_storecode`),
  KEY `WovenID` (`WovenID`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=latin1;
```

#### `abc_clubs` Table
```sql
CREATE TABLE `abc_clubs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `clubNumber` int(10) unsigned NOT NULL,
  `name` char(64) CHARACTER SET utf8 DEFAULT NULL,
  -- Additional fields...
  PRIMARY KEY (`id`),
  KEY `clubNumber` (`clubNumber`)
) ENGINE=InnoDB AUTO_INCREMENT=21 DEFAULT CHARSET=latin1;
```

#### `abc_company_settings` Table
```sql
CREATE TABLE `abc_company_settings` (
  `AvgDuesGoal` double DEFAULT NULL,
  `AvgDuesMid` double(5,2) DEFAULT NULL,
  `PeakPerGoal` double(5,2) DEFAULT NULL,
  `PeaksPerMid` double(5,2) DEFAULT NULL,
  `CheckingPercent` double(5,2) DEFAULT NULL,
  `CheckingPercentMid` double(5,2) DEFAULT NULL,
  `EmailPercent` double(5,2) DEFAULT NULL,
  `FacEqupWorking` double(10,2) DEFAULT NULL,
  `FacEqupWorkingMid` double(10,2) DEFAULT NULL,
  `FacOpenWO` int(11) DEFAULT NULL,
  `FacOpenWOMid` int(11) DEFAULT NULL,
  `FacAging` double(10,2) DEFAULT NULL,
  `FacAgingMid` int(11) DEFAULT NULL,
  `AttendPercent` double(10,2) DEFAULT NULL,
  `SchedulePercent` double(10,2) DEFAULT NULL,
  `UtilizationPercent` double(10,2) DEFAULT NULL,
  `ConversionPercent` double(10,2) DEFAULT NULL,
  `ShowPercent` double(10,2) DEFAULT NULL,
  `ShowPercentMid` double(10,2) DEFAULT NULL,
  `PPAGoal` double(10,2) DEFAULT '0.00',
  `PPAMid` double(10,2) DEFAULT '0.00',
  `PTRev` double(10,2) DEFAULT '0.00',
  `GFUtilization` double(10,2) DEFAULT NULL,
  `GFUtilizationMid` double(10,2) DEFAULT NULL,
  `C1KGoal` double(10,2) DEFAULT '0.00',
  `C1Kmid` double(10,2) DEFAULT '0.00'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;
```

### B. Key Relationships

#### Location Mapping
```sql
-- Woven assets to clubs:
woven_assets.location_id = abc_club_settings.WovenID
abc_club_settings.club_id = abc_clubs.id

-- Woven work orders to clubs:
woven_workorders.location_id = abc_club_settings.WovenID
abc_club_settings.club_id = abc_clubs.id

-- Work orders to assets:
woven_workorders.asset_id = woven_assets.woven_api_id
```

#### District Grouping
```sql
-- District grouping field:
abc_club_settings.MaintainDistrict

-- Example values: "Market 1", "Market 2", "District North", etc.
```

## 6. Naming Conventions and Method Signatures

### A. Model Method Naming Convention
```php
// Pattern: get_{metric}_{scope}
get_equipment_up($from, $to, $club_id)           // Equipment uptime by club
get_equipment_up_total_results()                 // Company-wide equipment uptime
get_workorder_opentime($club_id)                 // Work order aging by club
get_workorder_opentime_total_result()            // Company-wide aging
get_open_wo($club_id)                           // Open work orders by club
get_open_wo_today($club_id)                     // Today's opened work orders
get_closed_wo_today($club_id)                   // Today's closed work orders
get_open_wo_yesterday($club_id)                 // Yesterday's opened work orders
get_closed_wo_yesterday($club_id)               // Yesterday's closed work orders
```

### B. Data Structure Convention
```php
// Return format for all methods:
array_column($result, NULL, 'ClubNumber')

// Data structure for each club:
$data[$clubNumber] = [
    'MetricName' => [
        'value' => $numericValue,
        'color' => $hexColorCode
    ]
];

// Example:
$data[101]['UpPercentages'] = ['value' => 95.5, 'color' => '#00ff27'];
$data[101]['TotalWO'] = ['value' => 3, 'color' => '#00ff27'];
$data[101]['AvgOpen'] = ['value' => '2 day(s), 5 hour(s), 30 min(s)', 'color' => '#00ff27'];
```

### C. Color Coding Function Naming
```php
// Pattern: getFac{MetricType}Color
getFacEqupWorkingColor($value, $companySettings)  // Equipment uptime color
getFacOpenWO($value, $companySettings)            // Open work orders color
getFacDaysOpen($value, $companySettings)          // Aging color
```

### D. Company Settings Keys
```php
// Facility dashboard threshold settings:
'FacEqupWorking'     // Equipment uptime high threshold (0.95 = 95%)
'FacEqupWorkingMid'  // Equipment uptime medium threshold (0.85 = 85%)
'FacOpenWO'          // Open work orders low threshold (5)
'FacOpenWOMid'       // Open work orders medium threshold (10)
'FacAging'           // Aging low threshold in days (3)
'FacAgingMid'        // Aging medium threshold in days (7)
```

### E. SQL Field Naming
```php
// Calculated fields in SQL:
'UpPercentages'      // Equipment uptime percentage
'Assets'             // Total asset count
'WorkOrders'         // Assets with open work orders count
'TotalWO'            // Total open work orders
'AssetWO'            // Asset work orders count
'FacilityWO'         // Facility work orders count
'AvgOpen'            // Human-readable aging format
'DaysOpen'           // Aging in days (numeric)
'AVGTimeDiff'        // Aging in seconds
'OpenToday'          // Work orders opened today
'ClosedToday'        // Work orders closed today
'OpenYesterday'      // Work orders opened yesterday
'ClosedYesterday'    // Work orders closed yesterday
```

### F. View Variable Naming
```php
// Controller to view data passing:
$this->data['club_id'] = $club;                    // Selected club filter
$this->data['companySettings'] = $companySettings; // Threshold settings
$this->data['data'] = $data;                       // Club-level metrics
$this->data['total'] = $total;                     // Company-wide totals
$this->data['last_sync_date'] = $lastSync;         // Last data sync time
```

This comprehensive extraction provides all the specific code, formulas, business logic, data sources, and naming conventions needed to accurately recreate the facility dashboard in Laravel V2 while maintaining consistency with the original implementation.

# Team Member Dashboard - Laravel V2 Migration Guide

## Overview

This document provides a comprehensive guide for migrating the Team Member Dashboard from CodeIgniter to Laravel V2 with modern redesign. The migration includes database table copying, Laravel implementation, and modern UI/UX design.

## Migration Strategy

### Phase 1: Database Migration
Copy necessary database tables directly to <PERSON><PERSON> database to implement dashboard functionality independently.

### Phase 2: Laravel Implementation  
Create modern Laravel controllers, models, views, and routes using Laravel best practices.

### Phase 3: Modern UI/UX Design
Implement responsive, modern dashboard with real-time updates and interactive features.

---

## Database Tables to Copy

### Core Dashboard Tables
```sql
-- Main KPI data table (pre-calculated metrics)
CREATE TABLE monthly_tm_kpis (
    id INT PRIMARY KEY AUTO_INCREMENT,
    SalesPersonId VARCHAR(32),           -- Employee ID for filtering
    SalesPersonName VARCHAR(64),         -- Display name
    club_id INT,                         -- Club location filter
    Base INT DEFAULT 0,                  -- Base membership count
    Peak INT DEFAULT 0,                  -- Peak membership count
    PeakPlus INT DEFAULT 0,              -- Peak Plus membership count
    PeakResults INT DEFAULT 0,           -- Peak Results membership count
    AverageDues DECIMAL(10,2),           -- Average monthly dues
    PeaksPercent DECIMAL(5,2),           -- Percentage of premium memberships
    EFTRewards DECIMAL(10,2),            -- Commission earned
    SecondaryBilling DECIMAL(5,2),       -- Secondary billing percentage
    checkingPercent DECIMAL(5,2),        -- Checking account percentage
    TotalCH INT DEFAULT 0,               -- Total checking accounts
    TanningSales DECIMAL(10,2),          -- Tanning revenue
    TanningReward DECIMAL(10,2),         -- Tanning commission (10%)
    C1K10 INT DEFAULT 0,                 -- C1K consultation count
    C1K10Reward DECIMAL(10,2),           -- C1K commission ($5 each)
    FromDate DATE,                       -- Period start date
    ToDate DATE,                         -- Period end date
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_salesperson_club (SalesPersonId, club_id),
    INDEX idx_date_range (FromDate, ToDate),
    INDEX idx_club_date (club_id, FromDate)
);

-- Supporting employee data
CREATE TABLE abc_employees (
    id INT PRIMARY KEY AUTO_INCREMENT,
    employeeIdRemote VARCHAR(32) UNIQUE, -- ABC Financial ID
    personal_firstName VARCHAR(64),      -- First name
    personal_lastName VARCHAR(64),       -- Last name
    employment_employeeStatus TINYINT(1), -- 1=Active, 0=Terminated
    employment_startDate DATE,           -- Start date
    employment_terminationDate DATE,     -- Termination date
    employment_earningsCode VARCHAR(32), -- Commission classification
    club_id_local INT,                   -- Primary club assignment
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_employeeIdRemote (employeeIdRemote),
    INDEX idx_club_status (club_id_local, employment_employeeStatus)
);

-- Club information (if not already in Laravel V2)
CREATE TABLE abc_clubs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(128),                   -- Club name
    address VARCHAR(255),                -- Club address
    city VARCHAR(64),                    -- City
    state VARCHAR(32),                   -- State
    zip VARCHAR(16),                     -- ZIP code
    phone VARCHAR(32),                   -- Phone number
    status TINYINT(1) DEFAULT 1,         -- 1=Active, 0=Inactive
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_status (status),
    INDEX idx_name (name)
);
```

### Data Migration Script
```sql
-- Copy data from CodeIgniter database to Laravel database
-- Run this script to populate Laravel tables with existing data

-- Copy monthly KPI data (last 12 months)
INSERT INTO laravel_db.monthly_tm_kpis 
SELECT * FROM codeigniter_db.monthly_tm_kpis 
WHERE FromDate >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH);

-- Copy active employees
INSERT INTO laravel_db.abc_employees 
SELECT * FROM codeigniter_db.abc_employees 
WHERE employment_employeeStatus = 1;

-- Copy club information
INSERT INTO laravel_db.abc_clubs 
SELECT * FROM codeigniter_db.abc_clubs 
WHERE status = 1;
```

---

## Laravel Implementation

### 1. Eloquent Models

#### TeamMemberKpi Model
```php
<?php
// app/Models/TeamMemberKpi.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TeamMemberKpi extends Model
{
    protected $table = 'monthly_tm_kpis';
    
    protected $fillable = [
        'SalesPersonId', 'SalesPersonName', 'club_id',
        'Base', 'Peak', 'PeakPlus', 'PeakResults',
        'AverageDues', 'PeaksPercent', 'EFTRewards',
        'SecondaryBilling', 'checkingPercent', 'TotalCH',
        'TanningSales', 'TanningReward', 'C1K10', 'C1K10Reward',
        'FromDate', 'ToDate'
    ];
    
    protected $casts = [
        'Base' => 'integer',
        'Peak' => 'integer', 
        'PeakPlus' => 'integer',
        'PeakResults' => 'integer',
        'AverageDues' => 'decimal:2',
        'PeaksPercent' => 'decimal:2',
        'EFTRewards' => 'decimal:2',
        'SecondaryBilling' => 'decimal:2',
        'checkingPercent' => 'decimal:2',
        'TotalCH' => 'integer',
        'TanningSales' => 'decimal:2',
        'TanningReward' => 'decimal:2',
        'C1K10' => 'integer',
        'C1K10Reward' => 'decimal:2',
        'FromDate' => 'date',
        'ToDate' => 'date'
    ];
    
    // Relationships
    public function employee(): BelongsTo
    {
        return $this->belongsTo(Employee::class, 'SalesPersonId', 'employeeIdRemote');
    }
    
    public function club(): BelongsTo
    {
        return $this->belongsTo(Club::class, 'club_id');
    }
    
    // Scopes for filtering
    public function scopeForClub($query, $clubId)
    {
        return $query->where('club_id', $clubId);
    }
    
    public function scopeForDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('FromDate', [$startDate, $endDate]);
    }
    
    public function scopeForEmployee($query, $employeeId)
    {
        return $query->where('SalesPersonId', $employeeId);
    }
    
    // Calculated properties
    public function getTotalMembershipsAttribute()
    {
        return $this->Base + $this->Peak + $this->PeakPlus + $this->PeakResults;
    }
    
    public function getTotalCommissionAttribute()
    {
        return $this->EFTRewards + $this->TanningReward + $this->C1K10Reward;
    }
    
    public function getPremiumMembershipsAttribute()
    {
        return $this->Peak + $this->PeakPlus + $this->PeakResults;
    }
}
```

#### Employee Model
```php
<?php
// app/Models/Employee.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Employee extends Model
{
    protected $table = 'abc_employees';
    
    protected $fillable = [
        'employeeIdRemote', 'personal_firstName', 'personal_lastName',
        'employment_employeeStatus', 'employment_startDate', 
        'employment_terminationDate', 'employment_earningsCode', 'club_id_local'
    ];
    
    protected $casts = [
        'employment_employeeStatus' => 'boolean',
        'employment_startDate' => 'date',
        'employment_terminationDate' => 'date'
    ];
    
    // Relationships
    public function kpis(): HasMany
    {
        return $this->hasMany(TeamMemberKpi::class, 'SalesPersonId', 'employeeIdRemote');
    }
    
    public function club(): BelongsTo
    {
        return $this->belongsTo(Club::class, 'club_id_local');
    }
    
    // Scopes
    public function scopeActive($query)
    {
        return $query->where('employment_employeeStatus', 1);
    }
    
    public function scopeForClub($query, $clubId)
    {
        return $query->where('club_id_local', $clubId);
    }
    
    // Accessors
    public function getFullNameAttribute()
    {
        return $this->personal_firstName . ' ' . $this->personal_lastName;
    }
    
    public function getIsActiveAttribute()
    {
        return $this->employment_employeeStatus == 1;
    }
}
```

#### Club Model
```php
<?php
// app/Models/Club.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Club extends Model
{
    protected $table = 'abc_clubs';
    
    protected $fillable = [
        'name', 'address', 'city', 'state', 'zip', 'phone', 'status'
    ];
    
    protected $casts = [
        'status' => 'boolean'
    ];
    
    // Relationships
    public function employees(): HasMany
    {
        return $this->hasMany(Employee::class, 'club_id_local');
    }
    
    public function kpis(): HasMany
    {
        return $this->hasMany(TeamMemberKpi::class, 'club_id');
    }
    
    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
    
    // Accessors
    public function getFullAddressAttribute()
    {
        return $this->address . ', ' . $this->city . ', ' . $this->state . ' ' . $this->zip;
    }
}
```

### 2. Controllers

#### TeamMemberDashboardController
```php
<?php
// app/Http/Controllers/Admin/TeamMemberDashboardController.php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\TeamMemberKpi;
use App\Models\Employee;
use App\Models\Club;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Carbon\Carbon;

class TeamMemberDashboardController extends Controller
{
    /**
     * Display the team member dashboard
     */
    public function index(Request $request): View
    {
        $clubs = Club::active()->orderBy('name')->get();
        $selectedClub = $request->get('club_id', $clubs->first()?->id);
        
        // Default to current month
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth()->format('Y-m-d'));
        
        return view('admin.dashboard.team-member', compact(
            'clubs', 'selectedClub', 'startDate', 'endDate'
        ));
    }
    
    /**
     * Get dashboard data via AJAX
     */
    public function getData(Request $request): JsonResponse
    {
        $request->validate([
            'club_id' => 'required|integer|exists:abc_clubs,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);
        
        $kpis = TeamMemberKpi::with(['employee', 'club'])
            ->forClub($request->club_id)
            ->forDateRange($request->start_date, $request->end_date)
            ->orderBy('SalesPersonName')
            ->get();
        
        // Calculate summary statistics
        $summary = [
            'total_employees' => $kpis->count(),
            'total_memberships' => $kpis->sum('total_memberships'),
            'total_base' => $kpis->sum('Base'),
            'total_peak' => $kpis->sum('Peak'),
            'total_peak_plus' => $kpis->sum('PeakPlus'),
            'total_peak_results' => $kpis->sum('PeakResults'),
            'average_dues' => $kpis->avg('AverageDues'),
            'total_commission' => $kpis->sum('total_commission'),
            'total_tanning_sales' => $kpis->sum('TanningSales'),
            'total_consultations' => $kpis->sum('C1K10')
        ];
        
        return response()->json([
            'success' => true,
            'data' => $kpis,
            'summary' => $summary
        ]);
    }
    
    /**
     * Get employee performance details
     */
    public function getEmployeeDetails(Request $request, string $employeeId): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);
        
        $employee = Employee::where('employeeIdRemote', $employeeId)->firstOrFail();
        
        $kpis = TeamMemberKpi::with('club')
            ->forEmployee($employeeId)
            ->forDateRange($request->start_date, $request->end_date)
            ->orderBy('FromDate')
            ->get();
        
        return response()->json([
            'success' => true,
            'employee' => $employee,
            'performance' => $kpis
        ]);
    }
    
    /**
     * Export dashboard data to Excel
     */
    public function export(Request $request)
    {
        $request->validate([
            'club_id' => 'required|integer|exists:abc_clubs,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date'
        ]);
        
        // Implementation for Excel export
        // Use Laravel Excel package or similar
        
        return response()->download($filePath);
    }
}
```

### 3. Routes

#### Web Routes
```php
<?php
// routes/web.php - Add these routes to existing admin routes

Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {

    // Team Member Dashboard Routes
    Route::prefix('dashboard')->name('dashboard.')->group(function () {
        Route::get('/team-member', [TeamMemberDashboardController::class, 'index'])
            ->name('team-member');
        Route::get('/team-member/data', [TeamMemberDashboardController::class, 'getData'])
            ->name('team-member.data');
        Route::get('/team-member/employee/{employeeId}', [TeamMemberDashboardController::class, 'getEmployeeDetails'])
            ->name('team-member.employee');
        Route::get('/team-member/export', [TeamMemberDashboardController::class, 'export'])
            ->name('team-member.export');
    });

});
```

### 4. Modern Blade Templates

#### Main Dashboard View
```blade
{{-- resources/views/admin/dashboard/team-member.blade.php --}}

@extends('admin.layouts.app')

@section('title', 'Team Member Dashboard')

@section('content')
<div class="team-member-dashboard">
    <!-- Header Section -->
    <div class="dashboard-header bg-white shadow-sm rounded-lg p-6 mb-6">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Team Member Dashboard</h1>
                <p class="text-gray-600 mt-1">Track sales performance and KPI metrics</p>
            </div>

            <!-- Filters -->
            <div class="mt-4 lg:mt-0 flex flex-col sm:flex-row gap-4">
                <div class="min-w-0 flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Club</label>
                    <select id="club-filter" class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                        @foreach($clubs as $club)
                            <option value="{{ $club->id }}" {{ $club->id == $selectedClub ? 'selected' : '' }}>
                                {{ $club->name }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <div class="min-w-0 flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                    <input type="date" id="start-date" value="{{ $startDate }}"
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div class="min-w-0 flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                    <input type="date" id="end-date" value="{{ $endDate }}"
                           class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500">
                </div>

                <div class="flex items-end">
                    <button id="refresh-data" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md font-medium transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-users text-blue-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Employees</p>
                    <p class="text-2xl font-bold text-gray-900" id="total-employees">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-green-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-id-card text-green-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Memberships</p>
                    <p class="text-2xl font-bold text-gray-900" id="total-memberships">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-yellow-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-dollar-sign text-yellow-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Average Dues</p>
                    <p class="text-2xl font-bold text-gray-900" id="average-dues">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-sm p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-8 h-8 bg-purple-100 rounded-md flex items-center justify-center">
                        <i class="fas fa-chart-line text-purple-600"></i>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Total Commission</p>
                    <p class="text-2xl font-bold text-gray-900" id="total-commission">-</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Data Table -->
    <div class="bg-white shadow-sm rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-900">Team Performance</h2>
                <div class="flex gap-2">
                    <button id="export-excel" class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors">
                        <i class="fas fa-file-excel mr-2"></i>Export Excel
                    </button>
                </div>
            </div>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200" id="team-table">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Base</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peak</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peak+</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peak Results</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Dues</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Commission</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tanning</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">C1K</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peak %</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200" id="team-table-body">
                    <!-- Data will be populated via JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Loading State -->
        <div id="loading-state" class="flex items-center justify-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">Loading data...</span>
        </div>

        <!-- Empty State -->
        <div id="empty-state" class="hidden text-center py-12">
            <i class="fas fa-chart-bar text-gray-400 text-4xl mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
            <p class="text-gray-600">No team member data found for the selected filters.</p>
        </div>
    </div>
</div>

<!-- Employee Details Modal -->
<div id="employee-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900" id="modal-employee-name">Employee Details</h3>
                <button id="close-modal" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            <div id="modal-content">
                <!-- Employee details will be loaded here -->
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    .team-member-dashboard {
        min-height: calc(100vh - 200px);
    }

    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }

    .dashboard-header h1,
    .dashboard-header p {
        color: white;
    }

    .dashboard-header label {
        color: rgba(255, 255, 255, 0.9);
    }

    .table-row-hover:hover {
        background-color: #f8fafc;
        cursor: pointer;
    }

    .performance-badge {
        display: inline-flex;
        align-items: center;
        px-2.5 py-0.5;
        rounded-full text-xs font-medium;
    }

    .performance-excellent {
        background-color: #d1fae5;
        color: #065f46;
    }

    .performance-good {
        background-color: #dbeafe;
        color: #1e40af;
    }

    .performance-average {
        background-color: #fef3c7;
        color: #92400e;
    }

    .performance-poor {
        background-color: #fee2e2;
        color: #991b1b;
    }
</style>
@endpush

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const teamDashboard = new TeamMemberDashboard();
    teamDashboard.init();
});

class TeamMemberDashboard {
    constructor() {
        this.currentData = [];
        this.isLoading = false;
    }

    init() {
        this.bindEvents();
        this.loadData();
    }

    bindEvents() {
        // Filter change events
        document.getElementById('club-filter').addEventListener('change', () => this.loadData());
        document.getElementById('start-date').addEventListener('change', () => this.loadData());
        document.getElementById('end-date').addEventListener('change', () => this.loadData());
        document.getElementById('refresh-data').addEventListener('click', () => this.loadData());

        // Export button
        document.getElementById('export-excel').addEventListener('click', () => this.exportData());

        // Modal events
        document.getElementById('close-modal').addEventListener('click', () => this.closeModal());

        // Close modal on outside click
        document.getElementById('employee-modal').addEventListener('click', (e) => {
            if (e.target.id === 'employee-modal') {
                this.closeModal();
            }
        });
    }

    async loadData() {
        if (this.isLoading) return;

        this.isLoading = true;
        this.showLoading();

        try {
            const params = new URLSearchParams({
                club_id: document.getElementById('club-filter').value,
                start_date: document.getElementById('start-date').value,
                end_date: document.getElementById('end-date').value
            });

            const response = await fetch(`{{ route('admin.dashboard.team-member.data') }}?${params}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch data');
            }

            const result = await response.json();

            if (result.success) {
                this.currentData = result.data;
                this.updateSummaryCards(result.summary);
                this.renderTable(result.data);
            } else {
                throw new Error(result.message || 'Failed to load data');
            }

        } catch (error) {
            console.error('Error loading data:', error);
            this.showError('Failed to load dashboard data. Please try again.');
        } finally {
            this.isLoading = false;
            this.hideLoading();
        }
    }

    updateSummaryCards(summary) {
        document.getElementById('total-employees').textContent = summary.total_employees || 0;
        document.getElementById('total-memberships').textContent = summary.total_memberships || 0;
        document.getElementById('average-dues').textContent = summary.average_dues ? `$${parseFloat(summary.average_dues).toFixed(2)}` : '$0.00';
        document.getElementById('total-commission').textContent = summary.total_commission ? `$${parseFloat(summary.total_commission).toFixed(2)}` : '$0.00';
    }

    renderTable(data) {
        const tbody = document.getElementById('team-table-body');

        if (!data || data.length === 0) {
            this.showEmptyState();
            return;
        }

        this.hideEmptyState();

        tbody.innerHTML = data.map(employee => `
            <tr class="table-row-hover" onclick="teamDashboard.showEmployeeDetails('${employee.SalesPersonId}')">
                <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                                <span class="text-sm font-medium text-gray-700">
                                    ${this.getInitials(employee.SalesPersonName)}
                                </span>
                            </div>
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">${employee.SalesPersonName}</div>
                            <div class="text-sm text-gray-500">ID: ${employee.SalesPersonId}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.Base || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.Peak || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.PeakPlus || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.PeakResults || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$${parseFloat(employee.AverageDues || 0).toFixed(2)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$${parseFloat(employee.total_commission || 0).toFixed(2)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">$${parseFloat(employee.TanningSales || 0).toFixed(2)}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${employee.C1K10 || 0}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="performance-badge ${this.getPerformanceClass(employee.PeaksPercent)}">
                        ${parseFloat(employee.PeaksPercent || 0).toFixed(1)}%
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button onclick="event.stopPropagation(); teamDashboard.showEmployeeDetails('${employee.SalesPersonId}')"
                            class="text-blue-600 hover:text-blue-900">
                        <i class="fas fa-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }

    getInitials(name) {
        return name.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);
    }

    getPerformanceClass(percentage) {
        const pct = parseFloat(percentage || 0);
        if (pct >= 80) return 'performance-excellent';
        if (pct >= 60) return 'performance-good';
        if (pct >= 40) return 'performance-average';
        return 'performance-poor';
    }

    async showEmployeeDetails(employeeId) {
        try {
            const params = new URLSearchParams({
                start_date: document.getElementById('start-date').value,
                end_date: document.getElementById('end-date').value
            });

            const response = await fetch(`{{ route('admin.dashboard.team-member.employee', '') }}/${employeeId}?${params}`, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            });

            if (!response.ok) {
                throw new Error('Failed to fetch employee details');
            }

            const result = await response.json();

            if (result.success) {
                this.renderEmployeeModal(result.employee, result.performance);
                this.showModal();
            } else {
                throw new Error(result.message || 'Failed to load employee details');
            }

        } catch (error) {
            console.error('Error loading employee details:', error);
            this.showError('Failed to load employee details. Please try again.');
        }
    }

    renderEmployeeModal(employee, performance) {
        document.getElementById('modal-employee-name').textContent = employee.full_name;

        const modalContent = document.getElementById('modal-content');
        modalContent.innerHTML = `
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h4 class="text-lg font-medium text-gray-900 mb-3">Employee Information</h4>
                    <dl class="space-y-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Employee ID</dt>
                            <dd class="text-sm text-gray-900">${employee.employeeIdRemote}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Start Date</dt>
                            <dd class="text-sm text-gray-900">${new Date(employee.employment_startDate).toLocaleDateString()}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Status</dt>
                            <dd class="text-sm text-gray-900">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${employee.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                                    ${employee.is_active ? 'Active' : 'Inactive'}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Earnings Code</dt>
                            <dd class="text-sm text-gray-900">${employee.employment_earningsCode || 'N/A'}</dd>
                        </div>
                    </dl>
                </div>

                <div>
                    <h4 class="text-lg font-medium text-gray-900 mb-3">Performance Summary</h4>
                    <div class="space-y-3">
                        ${performance.map(kpi => `
                            <div class="bg-gray-50 rounded-lg p-3">
                                <div class="flex justify-between items-center mb-2">
                                    <span class="text-sm font-medium text-gray-900">
                                        ${new Date(kpi.FromDate).toLocaleDateString()} - ${new Date(kpi.ToDate).toLocaleDateString()}
                                    </span>
                                    <span class="text-sm text-gray-500">${kpi.club?.name || 'Unknown Club'}</span>
                                </div>
                                <div class="grid grid-cols-2 gap-2 text-xs">
                                    <div>Memberships: ${(kpi.Base || 0) + (kpi.Peak || 0) + (kpi.PeakPlus || 0) + (kpi.PeakResults || 0)}</div>
                                    <div>Commission: $${parseFloat(kpi.total_commission || 0).toFixed(2)}</div>
                                    <div>Avg Dues: $${parseFloat(kpi.AverageDues || 0).toFixed(2)}</div>
                                    <div>Peak %: ${parseFloat(kpi.PeaksPercent || 0).toFixed(1)}%</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }

    showModal() {
        document.getElementById('employee-modal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    }

    closeModal() {
        document.getElementById('employee-modal').classList.add('hidden');
        document.body.style.overflow = 'auto';
    }

    async exportData() {
        try {
            const params = new URLSearchParams({
                club_id: document.getElementById('club-filter').value,
                start_date: document.getElementById('start-date').value,
                end_date: document.getElementById('end-date').value
            });

            window.location.href = `{{ route('admin.dashboard.team-member.export') }}?${params}`;

        } catch (error) {
            console.error('Error exporting data:', error);
            this.showError('Failed to export data. Please try again.');
        }
    }

    showLoading() {
        document.getElementById('loading-state').classList.remove('hidden');
        document.getElementById('team-table-body').innerHTML = '';
        this.hideEmptyState();
    }

    hideLoading() {
        document.getElementById('loading-state').classList.add('hidden');
    }

    showEmptyState() {
        document.getElementById('empty-state').classList.remove('hidden');
        document.getElementById('team-table-body').innerHTML = '';
    }

    hideEmptyState() {
        document.getElementById('empty-state').classList.add('hidden');
    }

    showError(message) {
        // You can implement a toast notification system here
        alert(message);
    }
}
</script>
@endpush
```

### 5. Database Migrations

#### Create Migration Files
```php
<?php
// database/migrations/2024_01_01_000001_create_team_member_dashboard_tables.php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Monthly Team Member KPIs table
        Schema::create('monthly_tm_kpis', function (Blueprint $table) {
            $table->id();
            $table->string('SalesPersonId', 32)->index();
            $table->string('SalesPersonName', 64);
            $table->unsignedBigInteger('club_id')->index();
            $table->integer('Base')->default(0);
            $table->integer('Peak')->default(0);
            $table->integer('PeakPlus')->default(0);
            $table->integer('PeakResults')->default(0);
            $table->decimal('AverageDues', 10, 2)->nullable();
            $table->decimal('PeaksPercent', 5, 2)->nullable();
            $table->decimal('EFTRewards', 10, 2)->default(0);
            $table->decimal('SecondaryBilling', 5, 2)->nullable();
            $table->decimal('checkingPercent', 5, 2)->nullable();
            $table->integer('TotalCH')->default(0);
            $table->decimal('TanningSales', 10, 2)->default(0);
            $table->decimal('TanningReward', 10, 2)->default(0);
            $table->integer('C1K10')->default(0);
            $table->decimal('C1K10Reward', 10, 2)->default(0);
            $table->date('FromDate');
            $table->date('ToDate');
            $table->timestamps();

            $table->index(['SalesPersonId', 'club_id']);
            $table->index(['FromDate', 'ToDate']);
            $table->index(['club_id', 'FromDate']);
        });

        // ABC Employees table
        Schema::create('abc_employees', function (Blueprint $table) {
            $table->id();
            $table->string('employeeIdRemote', 32)->unique();
            $table->string('personal_firstName', 64);
            $table->string('personal_lastName', 64);
            $table->boolean('employment_employeeStatus')->default(1);
            $table->date('employment_startDate')->nullable();
            $table->date('employment_terminationDate')->nullable();
            $table->string('employment_earningsCode', 32)->nullable();
            $table->unsignedBigInteger('club_id_local')->nullable();
            $table->timestamps();

            $table->index(['club_id_local', 'employment_employeeStatus']);
        });

        // ABC Clubs table (if not exists in Laravel V2)
        if (!Schema::hasTable('abc_clubs')) {
            Schema::create('abc_clubs', function (Blueprint $table) {
                $table->id();
                $table->string('name', 128);
                $table->string('address', 255)->nullable();
                $table->string('city', 64)->nullable();
                $table->string('state', 32)->nullable();
                $table->string('zip', 16)->nullable();
                $table->string('phone', 32)->nullable();
                $table->boolean('status')->default(1);
                $table->timestamps();

                $table->index('status');
                $table->index('name');
            });
        }
    }

    public function down()
    {
        Schema::dropIfExists('monthly_tm_kpis');
        Schema::dropIfExists('abc_employees');
        Schema::dropIfExists('abc_clubs');
    }
};
```

### 6. Data Synchronization Service

#### TeamMemberDataSyncService
```php
<?php
// app/Services/TeamMemberDataSyncService.php

namespace App\Services;

use App\Models\TeamMemberKpi;
use App\Models\Employee;
use App\Models\Club;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class TeamMemberDataSyncService
{
    protected $codeIgniterDb;

    public function __construct()
    {
        // Configure CodeIgniter database connection
        $this->codeIgniterDb = DB::connection('codeigniter');
    }

    /**
     * Sync all team member data from CodeIgniter database
     */
    public function syncAllData(): array
    {
        $results = [
            'clubs' => 0,
            'employees' => 0,
            'kpis' => 0,
            'errors' => []
        ];

        try {
            DB::beginTransaction();

            // Sync clubs first
            $results['clubs'] = $this->syncClubs();

            // Sync employees
            $results['employees'] = $this->syncEmployees();

            // Sync KPI data (last 12 months)
            $results['kpis'] = $this->syncKpiData();

            DB::commit();

            Log::info('Team member data sync completed successfully', $results);

        } catch (\Exception $e) {
            DB::rollBack();
            $results['errors'][] = $e->getMessage();
            Log::error('Team member data sync failed', ['error' => $e->getMessage()]);
        }

        return $results;
    }

    /**
     * Sync club data
     */
    protected function syncClubs(): int
    {
        $clubs = $this->codeIgniterDb->table('abc_clubs')
            ->where('status', 1)
            ->get();

        $synced = 0;

        foreach ($clubs as $clubData) {
            Club::updateOrCreate(
                ['id' => $clubData->id],
                [
                    'name' => $clubData->name,
                    'address' => $clubData->address,
                    'city' => $clubData->city,
                    'state' => $clubData->state,
                    'zip' => $clubData->zip,
                    'phone' => $clubData->phone,
                    'status' => $clubData->status,
                    'created_at' => $clubData->created ?? now(),
                    'updated_at' => $clubData->updated ?? now()
                ]
            );
            $synced++;
        }

        return $synced;
    }

    /**
     * Sync employee data
     */
    protected function syncEmployees(): int
    {
        $employees = $this->codeIgniterDb->table('abc_employees')
            ->where('employment_employeeStatus', 1)
            ->get();

        $synced = 0;

        foreach ($employees as $empData) {
            Employee::updateOrCreate(
                ['employeeIdRemote' => $empData->employeeIdRemote],
                [
                    'personal_firstName' => $empData->personal_firstName,
                    'personal_lastName' => $empData->personal_lastName,
                    'employment_employeeStatus' => $empData->employment_employeeStatus,
                    'employment_startDate' => $empData->employment_startDate ?
                        Carbon::createFromTimestamp($empData->employment_startDate)->toDateString() : null,
                    'employment_terminationDate' => $empData->employment_terminationDate ?
                        Carbon::createFromTimestamp($empData->employment_terminationDate)->toDateString() : null,
                    'employment_earningsCode' => $empData->employment_earningsCode,
                    'club_id_local' => $empData->club_id_local,
                    'created_at' => $empData->created ?
                        Carbon::createFromTimestamp($empData->created) : now(),
                    'updated_at' => $empData->updated ?
                        Carbon::createFromTimestamp($empData->updated) : now()
                ]
            );
            $synced++;
        }

        return $synced;
    }

    /**
     * Sync KPI data (last 12 months)
     */
    protected function syncKpiData(): int
    {
        $startDate = Carbon::now()->subMonths(12)->startOfMonth();

        $kpis = $this->codeIgniterDb->table('monthly_tm_kpis')
            ->where('FromDate', '>=', $startDate->toDateString())
            ->get();

        $synced = 0;

        foreach ($kpis as $kpiData) {
            TeamMemberKpi::updateOrCreate(
                [
                    'SalesPersonId' => $kpiData->SalesPersonId,
                    'club_id' => $kpiData->club_id,
                    'FromDate' => $kpiData->FromDate,
                    'ToDate' => $kpiData->ToDate
                ],
                [
                    'SalesPersonName' => $kpiData->SalesPersonName,
                    'Base' => $kpiData->Base ?? 0,
                    'Peak' => $kpiData->Peak ?? 0,
                    'PeakPlus' => $kpiData->PeakPlus ?? 0,
                    'PeakResults' => $kpiData->PeakResults ?? 0,
                    'AverageDues' => $kpiData->AverageDues,
                    'PeaksPercent' => $kpiData->PeaksPercent,
                    'EFTRewards' => $kpiData->EFTRewards ?? 0,
                    'SecondaryBilling' => $kpiData->SecondaryBilling,
                    'checkingPercent' => $kpiData->checkingPercent,
                    'TotalCH' => $kpiData->TotalCH ?? 0,
                    'TanningSales' => $kpiData->TanningSales ?? 0,
                    'TanningReward' => $kpiData->TanningReward ?? 0,
                    'C1K10' => $kpiData->C1K10 ?? 0,
                    'C1K10Reward' => $kpiData->C1K10Reward ?? 0
                ]
            );
            $synced++;
        }

        return $synced;
    }

    /**
     * Sync only recent data (daily sync)
     */
    public function syncRecentData(): array
    {
        $results = [
            'kpis' => 0,
            'employees' => 0,
            'errors' => []
        ];

        try {
            // Sync KPI data for last 7 days
            $startDate = Carbon::now()->subDays(7);

            $kpis = $this->codeIgniterDb->table('monthly_tm_kpis')
                ->where('updated', '>=', $startDate->timestamp)
                ->get();

            foreach ($kpis as $kpiData) {
                TeamMemberKpi::updateOrCreate(
                    [
                        'SalesPersonId' => $kpiData->SalesPersonId,
                        'club_id' => $kpiData->club_id,
                        'FromDate' => $kpiData->FromDate,
                        'ToDate' => $kpiData->ToDate
                    ],
                    [
                        'SalesPersonName' => $kpiData->SalesPersonName,
                        'Base' => $kpiData->Base ?? 0,
                        'Peak' => $kpiData->Peak ?? 0,
                        'PeakPlus' => $kpiData->PeakPlus ?? 0,
                        'PeakResults' => $kpiData->PeakResults ?? 0,
                        'AverageDues' => $kpiData->AverageDues,
                        'PeaksPercent' => $kpiData->PeaksPercent,
                        'EFTRewards' => $kpiData->EFTRewards ?? 0,
                        'SecondaryBilling' => $kpiData->SecondaryBilling,
                        'checkingPercent' => $kpiData->checkingPercent,
                        'TotalCH' => $kpiData->TotalCH ?? 0,
                        'TanningSales' => $kpiData->TanningSales ?? 0,
                        'TanningReward' => $kpiData->TanningReward ?? 0,
                        'C1K10' => $kpiData->C1K10 ?? 0,
                        'C1K10Reward' => $kpiData->C1K10Reward ?? 0
                    ]
                );
                $results['kpis']++;
            }

            // Sync recently updated employees
            $employees = $this->codeIgniterDb->table('abc_employees')
                ->where('updated', '>=', $startDate->timestamp)
                ->get();

            foreach ($employees as $empData) {
                Employee::updateOrCreate(
                    ['employeeIdRemote' => $empData->employeeIdRemote],
                    [
                        'personal_firstName' => $empData->personal_firstName,
                        'personal_lastName' => $empData->personal_lastName,
                        'employment_employeeStatus' => $empData->employment_employeeStatus,
                        'employment_startDate' => $empData->employment_startDate ?
                            Carbon::createFromTimestamp($empData->employment_startDate)->toDateString() : null,
                        'employment_terminationDate' => $empData->employment_terminationDate ?
                            Carbon::createFromTimestamp($empData->employment_terminationDate)->toDateString() : null,
                        'employment_earningsCode' => $empData->employment_earningsCode,
                        'club_id_local' => $empData->club_id_local,
                        'updated_at' => now()
                    ]
                );
                $results['employees']++;
            }

            Log::info('Recent team member data sync completed', $results);

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            Log::error('Recent team member data sync failed', ['error' => $e->getMessage()]);
        }

        return $results;
    }
}
```

### 7. Artisan Commands

#### Data Sync Command
```php
<?php
// app/Console/Commands/SyncTeamMemberData.php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TeamMemberDataSyncService;

class SyncTeamMemberData extends Command
{
    protected $signature = 'team-member:sync {--recent : Sync only recent data}';
    protected $description = 'Sync team member dashboard data from CodeIgniter database';

    protected $syncService;

    public function __construct(TeamMemberDataSyncService $syncService)
    {
        parent::__construct();
        $this->syncService = $syncService;
    }

    public function handle()
    {
        $this->info('Starting team member data synchronization...');

        if ($this->option('recent')) {
            $results = $this->syncService->syncRecentData();
            $this->info('Recent data sync completed.');
        } else {
            $results = $this->syncService->syncAllData();
            $this->info('Full data sync completed.');
        }

        // Display results
        $this->table(
            ['Type', 'Count'],
            [
                ['Clubs', $results['clubs'] ?? 0],
                ['Employees', $results['employees'] ?? 0],
                ['KPIs', $results['kpis'] ?? 0]
            ]
        );

        if (!empty($results['errors'])) {
            $this->error('Errors occurred during sync:');
            foreach ($results['errors'] as $error) {
                $this->error("- {$error}");
            }
            return 1;
        }

        $this->info('Synchronization completed successfully!');
        return 0;
    }
}
```

### 8. Configuration & Environment

#### Database Configuration
```php
// config/database.php - Add CodeIgniter database connection

'connections' => [
    // ... existing connections

    'codeigniter' => [
        'driver' => 'mysql',
        'host' => env('CI_DB_HOST', '127.0.0.1'),
        'port' => env('CI_DB_PORT', '3306'),
        'database' => env('CI_DB_DATABASE', 'codeigniter_db'),
        'username' => env('CI_DB_USERNAME', 'forge'),
        'password' => env('CI_DB_PASSWORD', ''),
        'unix_socket' => env('CI_DB_SOCKET', ''),
        'charset' => 'utf8mb4',
        'collation' => 'utf8mb4_unicode_ci',
        'prefix' => '',
        'prefix_indexes' => true,
        'strict' => true,
        'engine' => null,
        'options' => extension_loaded('pdo_mysql') ? array_filter([
            PDO::MYSQL_ATTR_SSL_CA => env('MYSQL_ATTR_SSL_CA'),
        ]) : [],
    ],
],
```

#### Environment Variables
```env
# .env - Add these variables

# CodeIgniter Database Connection (for data sync)
CI_DB_HOST=127.0.0.1
CI_DB_PORT=3306
CI_DB_DATABASE=tribe
CI_DB_USERNAME=root
CI_DB_PASSWORD=your_password

# Team Member Dashboard Settings
TEAM_DASHBOARD_CACHE_TTL=3600
TEAM_DASHBOARD_EXPORT_LIMIT=1000
```

### 9. Scheduled Tasks

#### Task Scheduling
```php
// app/Console/Kernel.php

protected function schedule(Schedule $schedule)
{
    // Sync recent team member data every hour
    $schedule->command('team-member:sync --recent')
        ->hourly()
        ->withoutOverlapping()
        ->runInBackground();

    // Full sync daily at 3 AM
    $schedule->command('team-member:sync')
        ->dailyAt('03:00')
        ->withoutOverlapping()
        ->runInBackground();
}
```

### 10. Testing

#### Feature Tests
```php
<?php
// tests/Feature/TeamMemberDashboardTest.php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\TeamMemberKpi;
use App\Models\Employee;
use App\Models\Club;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Carbon\Carbon;

class TeamMemberDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $club;
    protected $employee;

    protected function setUp(): void
    {
        parent::setUp();

        $this->user = User::factory()->create(['role' => 'admin']);
        $this->club = Club::factory()->create();
        $this->employee = Employee::factory()->create(['club_id_local' => $this->club->id]);
    }

    public function test_dashboard_page_loads()
    {
        $response = $this->actingAs($this->user)
            ->get(route('admin.dashboard.team-member'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard.team-member');
        $response->assertViewHas(['clubs', 'selectedClub', 'startDate', 'endDate']);
    }

    public function test_dashboard_data_api()
    {
        // Create test KPI data
        TeamMemberKpi::factory()->create([
            'SalesPersonId' => $this->employee->employeeIdRemote,
            'club_id' => $this->club->id,
            'FromDate' => Carbon::now()->startOfMonth(),
            'ToDate' => Carbon::now()->endOfMonth(),
            'Base' => 5,
            'Peak' => 3,
            'PeakPlus' => 2,
            'PeakResults' => 1
        ]);

        $response = $this->actingAs($this->user)
            ->getJson(route('admin.dashboard.team-member.data', [
                'club_id' => $this->club->id,
                'start_date' => Carbon::now()->startOfMonth()->format('Y-m-d'),
                'end_date' => Carbon::now()->endOfMonth()->format('Y-m-d')
            ]));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'data' => [
                '*' => [
                    'SalesPersonId',
                    'SalesPersonName',
                    'club_id',
                    'Base',
                    'Peak',
                    'PeakPlus',
                    'PeakResults',
                    'total_memberships',
                    'total_commission'
                ]
            ],
            'summary' => [
                'total_employees',
                'total_memberships',
                'total_base',
                'total_peak',
                'average_dues',
                'total_commission'
            ]
        ]);
    }

    public function test_employee_details_api()
    {
        TeamMemberKpi::factory()->create([
            'SalesPersonId' => $this->employee->employeeIdRemote,
            'club_id' => $this->club->id
        ]);

        $response = $this->actingAs($this->user)
            ->getJson(route('admin.dashboard.team-member.employee', $this->employee->employeeIdRemote), [
                'start_date' => Carbon::now()->startOfMonth()->format('Y-m-d'),
                'end_date' => Carbon::now()->endOfMonth()->format('Y-m-d')
            ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'success',
            'employee' => [
                'employeeIdRemote',
                'full_name',
                'is_active',
                'employment_startDate'
            ],
            'performance'
        ]);
    }

    public function test_unauthorized_access_denied()
    {
        $response = $this->get(route('admin.dashboard.team-member'));
        $response->assertRedirect(route('login'));
    }

    public function test_data_filtering_by_club()
    {
        $club2 = Club::factory()->create();

        TeamMemberKpi::factory()->create([
            'club_id' => $this->club->id,
            'Base' => 5
        ]);

        TeamMemberKpi::factory()->create([
            'club_id' => $club2->id,
            'Base' => 3
        ]);

        $response = $this->actingAs($this->user)
            ->getJson(route('admin.dashboard.team-member.data', [
                'club_id' => $this->club->id,
                'start_date' => Carbon::now()->startOfMonth()->format('Y-m-d'),
                'end_date' => Carbon::now()->endOfMonth()->format('Y-m-d')
            ]));

        $response->assertStatus(200);
        $data = $response->json('data');

        $this->assertCount(1, $data);
        $this->assertEquals($this->club->id, $data[0]['club_id']);
    }
}
```

### 11. Deployment Steps

#### Step-by-Step Migration Process

1. **Prepare Laravel Environment**
```bash
# Install required packages
composer require maatwebsite/excel
composer require laravel/ui

# Run migrations
php artisan migrate

# Register commands
php artisan make:command SyncTeamMemberData
```

2. **Configure Database Connections**
```bash
# Add CodeIgniter database credentials to .env
# Configure database connections in config/database.php
```

3. **Initial Data Migration**
```bash
# Run full data sync
php artisan team-member:sync

# Verify data migration
php artisan tinker
>>> App\Models\TeamMemberKpi::count()
>>> App\Models\Employee::count()
>>> App\Models\Club::count()
```

4. **Set Up Scheduled Tasks**
```bash
# Add to crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

5. **Test Dashboard**
```bash
# Run tests
php artisan test --filter TeamMemberDashboardTest

# Start development server
php artisan serve
```

6. **Production Deployment**
```bash
# Optimize for production
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Set up queue workers for background sync
php artisan queue:work --daemon
```

### 12. Modern UI Features

#### Key Design Improvements

1. **Responsive Design**: Mobile-first approach with Tailwind CSS
2. **Real-time Updates**: AJAX-powered data loading without page refresh
3. **Interactive Elements**: Modal dialogs, hover effects, loading states
4. **Performance Indicators**: Color-coded performance badges
5. **Export Functionality**: Excel export with filtered data
6. **Search & Filter**: Advanced filtering by club, date range, employee
7. **Data Visualization**: Charts and graphs for performance trends
8. **Accessibility**: ARIA labels, keyboard navigation, screen reader support

#### Additional Features to Consider

1. **Real-time Notifications**: WebSocket integration for live updates
2. **Advanced Analytics**: Charts using Chart.js or similar
3. **Performance Comparisons**: Month-over-month, year-over-year comparisons
4. **Goal Tracking**: Set and track performance goals
5. **Mobile App**: Progressive Web App (PWA) capabilities
6. **API Integration**: RESTful API for mobile apps or third-party integrations

This comprehensive migration guide provides everything needed to successfully move the Team Member Dashboard from CodeIgniter to Laravel V2 with modern design and enhanced functionality.
```
```

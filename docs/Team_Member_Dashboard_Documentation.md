# Team Member Dashboard - Comprehensive Documentation

## Table of Contents
1. [Overview](#overview)
2. [System Architecture](#system-architecture)
3. [Database Schema](#database-schema)
4. [Business Logic](#business-logic)
5. [Technical Implementation](#technical-implementation)
6. [User Interface](#user-interface)
7. [Security & Access Control](#security--access-control)
8. [API Endpoints](#api-endpoints)
9. [Dependencies](#dependencies)
10. [Data Flow](#data-flow)

## Overview

The Team Member Dashboard (DTM - Daily Team Member) is a comprehensive performance tracking and analytics system designed for fitness club management. It provides real-time and historical insights into team member sales performance, membership metrics, and key performance indicators (KPIs).

### Key Features
- **Real-time Performance Tracking**: Daily, monthly, and historical sales data
- **Multi-Club Support**: Aggregate and individual club performance views
- **Membership Analytics**: Base, Peak, and Peak Results membership tracking
- **Revenue Metrics**: Average dues, rewards, and commission calculations
- **Sorting & Filtering**: Dynamic data organization and club-specific filtering
- **Historical Analysis**: Month-to-date (MTD) and 6-month average comparisons

## System Architecture

### Framework & Technology Stack
- **Backend**: CodeIgniter 3.x (PHP MVC Framework)
- **Frontend**: HTML5, CSS3, JavaScript (jQuery)
- **Database**: MySQL
- **Authentication**: Ion Auth Library
- **UI Framework**: AdminLTE (Bootstrap-based)

### Core Components
```
application/
├── controllers/admin/Dashboard.php     # Main dashboard controller
├── models/admin/Team_model.php         # Data access layer
├── views/admin/dashboard/              # View templates
│   ├── daily-team-member-*.php        # Daily views
│   ├── mtd-team-member.php            # Monthly views
│   └── team-member-*.php              # Various team views
└── assets/
    ├── js/dashboard.js                # Frontend interactions
    └── css/                           # Styling
```

## Database Schema

### Primary Tables

#### `abc_employees`
```sql
CREATE TABLE `abc_employees` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `club_id_local` int(10) unsigned NOT NULL,
  `employeeIdRemote` char(32) DEFAULT NULL,
  `personal_firstName` char(32) DEFAULT NULL,
  `personal_middleInitial` char(2) DEFAULT NULL,
  `personal_lastName` char(32) DEFAULT NULL,
  `employment_employeeStatus` tinyint(4) DEFAULT '1',
  `employment_earningsCode` char(32) DEFAULT NULL,
  `employment_startDate` date DEFAULT NULL,
  `employment_terminationDate` date DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `employeeIdRemote` (`employeeIdRemote`),
  KEY `club_id_local` (`club_id_local`)
);
```

#### `monthly_tm_kpis`
```sql
CREATE TABLE `monthly_tm_kpis` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `SalesPersonId` char(32) DEFAULT NULL,
  `SalesPersonName` varchar(64) DEFAULT NULL,
  `club_id` int(11) DEFAULT NULL,
  `FromDate` date DEFAULT NULL,
  `ToDate` date DEFAULT NULL,
  `Base` int(11) DEFAULT '0',
  `Peak` int(11) DEFAULT '0',
  `PeakResults` int(11) DEFAULT '0',
  `TotalMems` int(11) DEFAULT '0',
  `AverageDues` decimal(10,2) DEFAULT '0.00',
  `PeaksPercent` decimal(5,4) DEFAULT '0.0000',
  `CheckingPercent` decimal(5,2) DEFAULT '0.00',
  `C1K10` int(11) DEFAULT '0',
  `TanningReward` decimal(10,2) DEFAULT '0.00',
  `UpgradeReward` decimal(10,2) DEFAULT '0.00',
  `EFTRewards` decimal(10,2) DEFAULT '0.00',
  PRIMARY KEY (`id`),
  KEY `SalesPersonId` (`SalesPersonId`),
  KEY `club_id` (`club_id`),
  KEY `FromDate` (`FromDate`)
);
```

#### `udt_agreements`
```sql
CREATE TABLE `udt_agreements` (
  `club_id` int(10) unsigned DEFAULT '0',
  `agreement_id` int(10) unsigned NOT NULL DEFAULT '0',
  `member_id` int(11) NOT NULL,
  `Date` date DEFAULT NULL,
  `agreementNumber` char(16) DEFAULT NULL,
  `firstname` char(64) DEFAULT NULL,
  `lastname` char(64) DEFAULT NULL,
  `membershipType` char(32) DEFAULT NULL,
  `SalesPersonId` char(32) DEFAULT NULL,
  `SalesPersonName` char(64) DEFAULT NULL,
  `signDate` date DEFAULT NULL,
  `lastRewriteDate` date DEFAULT NULL,
  KEY `SalesPersonId` (`SalesPersonId`),
  KEY `club_id` (`club_id`),
  KEY `Date` (`Date`)
);
```

### Supporting Tables
- `abc_clubs`: Club information and settings
- `abc_members_agreements`: Member agreement details
- `abc_payment_plans`: Payment plan configurations
- `abc_member_statuses`: Member status tracking
- `daily_kpis`: Daily performance metrics

## Business Logic

### Membership Categories
1. **Base Memberships**: Entry-level gym access
2. **Peak Memberships**: Enhanced access with additional amenities
3. **Peak Plus**: Premium tier with extended benefits
4. **Peak Results**: Highest tier with personal training inclusion

### Performance Metrics
- **Total Members Sold**: Sum of all membership types
- **Average Dues**: Revenue per membership calculation
- **Peak Percentage**: Ratio of premium to total memberships
- **Checking Percentage**: Payment method preference tracking
- **C1K (Consultation 1000)**: Personal training consultation metrics
- **Rewards**: Commission and incentive calculations

### Data Aggregation Rules
- **Active Members**: Status = 1 OR cancellation > 30 days from sign date
- **Exclusions**: Employee, Corporate, and Upgraded/Downgraded memberships
- **Date Filtering**: Configurable date ranges for performance periods
- **Club Filtering**: Individual club or aggregate "all clubs" view

## Technical Implementation

### Controller Methods

#### `Dashboard::team()`
- **Purpose**: Main team member dashboard view
- **Parameters**: club, from, to, sort_column, sort_dir
- **Data Sources**: Team_model::get_dtm_data()
- **View**: admin/dashboard/daily-team-member

#### `Dashboard::team_mtd()`
- **Purpose**: Month-to-date historical analysis
- **Parameters**: employeeIdRemote
- **Data Sources**: 
  - Team_model::get_teammember_mtd()
  - Team_model::get_teammember_mtd_avg()
- **View**: admin/dashboard/mtd-team-member

#### `Dashboard::team_daily()`
- **Purpose**: Daily performance tracking
- **Access Control**: DTM permission required
- **Features**: Real-time data with club filtering

### Model Methods

#### `Team_model::get_dtm_data($from, $to, $club)`
```php
// Aggregates membership sales data by team member
// Filters by date range and club
// Returns structured array with totals and individual performance
```

#### `Team_model::get_teammember_mtd($employee)`
```php
// Historical monthly data for specific employee
// 6-month rolling averages
// Performance trend analysis
```

#### Key SQL Patterns
```sql
-- Membership counting with exclusions
SELECT SalesPersonId, SalesPersonName,
       COUNT(CASE WHEN membershipType LIKE 'Base%' THEN 1 END) as base,
       COUNT(CASE WHEN membershipType IN ('Peak', 'PEAK SILVER') THEN 1 END) as peak,
       COUNT(CASE WHEN membershipType LIKE 'Peak Results%' THEN 1 END) as peak_results
FROM udt_agreements a
LEFT JOIN abc_members_personaldata p ON p.member_id = a.member_id
LEFT JOIN abc_member_statuses s ON s.id = p.memberStatus_id
LEFT JOIN abc_payment_plans pp ON a.paymentPlan_id = pp.id
WHERE a.Date >= ? AND a.Date <= ?
  AND (s.active = 1 OR DATEDIFF(p.memberStatusDate, a.lastRewriteDate) > 30)
  AND pp.Employee = 0 AND pp.upgraded = 0 AND pp.downgraded = 0
GROUP BY SalesPersonId;
```

## User Interface

### Layout Structure
- **Header**: Club logo, date selectors, filtering controls
- **Data Table**: Sortable columns with performance metrics
- **Footer**: Totals row with aggregate calculations
- **Styling**: Responsive design with AdminLTE theme

### Interactive Features
- **Column Sorting**: Click headers to sort by any metric
- **Date Range Selection**: Custom date filtering
- **Club Filtering**: Dropdown for individual or all clubs
- **Real-time Updates**: Form submission triggers data refresh

### CSS Classes
```css
.dtm-table {
    /* Main table styling */
    border: 1px solid black;
    width: auto;
}

.dtm-table th {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 10px;
}

.dtm-table td.spname {
    background-color: #333;
    color: white;
    text-align: left;
}

.sortable {
    cursor: pointer;
}

.sortable-asc-active { color: red; }
.sortable-desc-active { color: #00ff27; }
```

## Security & Access Control

### Authentication Requirements
- **Ion Auth**: User must be logged in
- **Permission Check**: `userHasAccessTo('DTM')` required
- **Group Access**: Admin, GM, AGM, RM roles supported

### Access Control Implementation
```php
// In Dashboard controller
public function team_daily() {
    $this->userHasAccessTo('DTM');
    // ... rest of method
}

// In MY_Controller base class
public function userHasAccessTo(string $access_key, ?array $permission_groups = NULL) {
    if (!$this->verifyUserAccessKeysOR($access_key) || 
        (!empty($permission_groups) && !$this->ion_auth->in_group($permission_groups))) {
        redirect('/', 'refresh');
        die;
    }
}
```

### Permission System
- **Access Keys**: JSON-stored user permissions
- **Group-based**: Role hierarchy with inheritance
- **Page-level**: Granular access control per dashboard section

## API Endpoints

### Primary Routes
```php
// Main dashboard
GET /admin/dashboard/team
GET /admin/dashboard/team_daily  
GET /admin/dashboard/team_mtd
GET /admin/dashboard/team_lastmonth

// AJAX endpoints
POST /admin/dashboard/save_active_etf_members_goal
```

### Parameters
- **club**: Club ID or 'all' for aggregate view
- **from/to**: Date range in Y-m-d H:i:s format
- **sort_column**: Field name for sorting
- **sort_dir**: 0 (ASC) or 1 (DESC)
- **employeeIdRemote**: Specific employee filter

## Dependencies

### Core Libraries
- **CodeIgniter 3.x**: MVC framework
- **Ion Auth**: Authentication and authorization
- **jQuery**: Frontend interactions
- **AdminLTE**: UI theme and components

### Models
- `Team_model`: Primary data access
- `Abcfinancial_clubs_model`: Club information
- `Abcfinancial_employees_model`: Employee data
- `Dashboard_model`: Supporting dashboard functions

### External Integrations
- **ABC Financial API**: Member and employee data sync
- **Sync Services**: Automated data updates
- **Reporting Engine**: Export and analysis tools

## Data Flow

### Request Lifecycle
1. **Authentication**: Ion Auth validates user session
2. **Authorization**: DTM permission verification
3. **Parameter Processing**: Date ranges, club selection, sorting
4. **Data Retrieval**: Team_model queries with filters
5. **Data Aggregation**: Calculations and totals
6. **View Rendering**: Template processing with data
7. **Response**: HTML with embedded JavaScript for interactions

### Data Synchronization
- **Scheduled Sync**: Regular ABC Financial API updates
- **Real-time Processing**: Live data for current day metrics
- **Cache Strategy**: Optimized queries for historical data
- **Error Handling**: Graceful degradation for sync failures

### Performance Optimization
- **Indexed Queries**: Strategic database indexing
- **Query Optimization**: Efficient JOIN operations
- **Pagination**: Large dataset handling
- **Caching**: Reduced database load for static data

## Frontend JavaScript Implementation

### Dashboard Interactions
```javascript
// Main dashboard functionality
$(document).ready(function() {
    // Club dropdown change handler
    $('#ut-active-eft-members-form').on('change', '#ut-club-dropdown', function (e) {
        $('#ut-active-eft-members-form').submit();
    });

    // Goal setting modal
    $('#set-goal-modal').on('click', '.btn-save-el', function(e) {
        e.preventDefault();
        $.post('/admin/dashboard/save_active_etf_members_goal', {
            club_id: $('#ut-club-dropdown').val(),
            goal: $('#ut-goal-input').val(),
            duesgoal: $('#ut-duesgoal-input').val(),
            checkgoal: $('#ut-checkgoal-input').val()
        }).always(function(result) {
            let response = result.responseJSON || result;
            $('#set-goal-modal').modal('hide');
            if (response && response.success) {
                location.reload();
            }
        });
    });
});
```

### Sorting Implementation
```javascript
// Column sorting functionality
$('.sortable').click(function () {
    var column = $(this).attr('column');
    $('#sort_column').val(column);

    var sort_dir = $('#sort_dir').val();
    if (sort_dir == '') {
        sort_dir = '1';
    } else {
        sort_dir = (sort_dir == '1') ? 0 : 1;
    }

    $('#sort_dir').val(sort_dir);
    $('.sortable-form').submit();
});
```

## View Templates Structure

### Main Dashboard Views

#### `daily-team-member-today.php`
- **Purpose**: Current day performance tracking
- **Features**: Real-time sales data, sortable columns
- **Metrics**: Base/Peak/Peak Results counts, average dues, rewards

#### `daily-team-member-new.php`
- **Purpose**: Enhanced daily view with additional metrics
- **Features**: Improved UI, additional KPIs
- **Enhancements**: Better responsive design

#### `daily-team-member-lastmonth.php`
- **Purpose**: Previous month performance review
- **Features**: Historical comparison, trend analysis
- **Data**: Month-end totals, employee rankings

#### `mtd-team-member.php`
- **Purpose**: Month-to-date and historical analysis
- **Features**: 6-month averages, monthly breakdowns
- **Analytics**: Performance trends, goal tracking

### Template Structure
```php
<div class="content-wrapper">
    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">Team Member Dashboard</h3>
                        <span class="page-last-date">Last Sync: <?= $last_sync_date ?></span>
                    </div>
                    <div class="box-body">
                        <!-- Filter Form -->
                        <form method="GET" class="sortable-form">
                            <!-- Club Selection -->
                            <!-- Date Range -->
                            <!-- Sort Controls -->
                        </form>

                        <!-- Data Table -->
                        <table class="dtm-table">
                            <thead>
                                <tr>
                                    <th class="sortable" column="salesPersonName">Team Member</th>
                                    <th class="sortable" column="base">Base</th>
                                    <th class="sortable" column="peak">Peak</th>
                                    <!-- Additional columns -->
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($data as $person): ?>
                                <tr>
                                    <td class="spname"><?= $person['salesPersonName'] ?></td>
                                    <td><?= $person['base'] ?: '-' ?></td>
                                    <!-- Additional data cells -->
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                            <tfoot>
                                <tr class="totals-row">
                                    <!-- Aggregate totals -->
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
```

## Error Handling & Logging

### Exception Management
```php
// In Team_model methods
try {
    $result = $CI->db->query($query)->result_array();
    if (!$result) {
        log_message('error', 'DTM Query failed: ' . $CI->db->last_query());
        return array();
    }
    return $result;
} catch (Exception $e) {
    log_message('error', 'DTM Exception: ' . $e->getMessage());
    return array();
}
```

### Data Validation
- **Date Range Validation**: Ensure valid date formats and ranges
- **Club ID Validation**: Verify club exists and user has access
- **Employee ID Validation**: Check employee exists and is active
- **SQL Injection Prevention**: Parameterized queries and input sanitization

## Configuration & Settings

### Company Settings
```php
// Retrieved via Team_model::getCompanySettings()
$companySettings = array(
    'AvgDuesGoal' => '29.99',      // Target average dues
    'CheckingGoal' => '85.00',     // Checking percentage target
    'PeakGoal' => '60.00',         // Peak membership percentage goal
    // Additional KPI targets
);
```

### Club Configuration
- **Multi-club Support**: Aggregate and individual club views
- **Club-specific Goals**: Customizable targets per location
- **Regional Grouping**: District-level reporting capabilities

## Integration Points

### ABC Financial API
- **Member Sync**: Regular synchronization of member data
- **Employee Sync**: Staff information updates
- **Agreement Sync**: Membership agreement processing
- **Status Updates**: Real-time membership status changes

### Sync Process Flow
1. **Scheduled Execution**: CRON-based sync jobs
2. **Data Extraction**: API calls to ABC Financial
3. **Data Transformation**: Format conversion and validation
4. **Data Loading**: Database updates with conflict resolution
5. **Sync Logging**: Success/failure tracking and notifications

## Troubleshooting Guide

### Common Issues

#### Data Sync Problems
- **Symptom**: Missing or outdated team member data
- **Diagnosis**: Check `abc_sync_employees` table for sync status
- **Resolution**: Verify API credentials, check sync logs, manual sync trigger

#### Performance Issues
- **Symptom**: Slow dashboard loading
- **Diagnosis**: Check query execution times, database locks
- **Resolution**: Optimize queries, add indexes, implement caching

#### Access Control Issues
- **Symptom**: Users cannot access DTM dashboard
- **Diagnosis**: Verify user permissions in `users.tmp_access_to`
- **Resolution**: Update user access keys, check group memberships

### Debugging Tools
```php
// Enable query debugging
$this->db->db_debug = TRUE;

// Log query execution
log_message('debug', 'DTM Query: ' . $this->db->last_query());

// Performance monitoring
$start_time = microtime(true);
// ... execute query ...
$execution_time = microtime(true) - $start_time;
log_message('info', 'DTM Query Time: ' . $execution_time . 's');
```

## Future Enhancements

### Planned Features
1. **Real-time Notifications**: Push alerts for performance milestones
2. **Mobile Responsiveness**: Enhanced mobile dashboard experience
3. **Advanced Analytics**: Predictive modeling and trend analysis
4. **Export Functionality**: PDF/Excel report generation
5. **Goal Management**: Dynamic goal setting and tracking
6. **Team Comparisons**: Cross-team performance analysis

### Technical Improvements
- **API Modernization**: RESTful API endpoints
- **Frontend Framework**: Vue.js or React integration
- **Database Optimization**: Query performance enhancements
- **Caching Layer**: Redis implementation for improved speed
- **Microservices**: Service-oriented architecture migration

## Data Dictionary

### Key Metrics Definitions

| Metric | Definition | Calculation | Business Impact |
|--------|------------|-------------|-----------------|
| **Base Memberships** | Entry-level gym memberships | COUNT(membershipType LIKE 'Base%') | Foundation revenue stream |
| **Peak Memberships** | Premium memberships with enhanced access | COUNT(membershipType IN ('Peak', 'PEAK SILVER')) | Higher revenue per member |
| **Peak Results** | Top-tier memberships with PT inclusion | COUNT(membershipType LIKE 'Peak Results%') | Highest value memberships |
| **Total Members Sold** | Sum of all membership types | Base + Peak + Peak Results | Overall sales performance |
| **Average Dues** | Revenue per membership | SUM(dues) / COUNT(memberships) | Revenue efficiency |
| **Peak Percentage** | Ratio of premium to total sales | (Peak + Peak Results) / Total * 100 | Upselling effectiveness |
| **Checking Percentage** | EFT payment adoption rate | COUNT(checking_payments) / Total * 100 | Payment reliability |
| **C1K (Consultation)** | Personal training consultations | COUNT(pt_consultations) | PT lead generation |
| **Rewards** | Commission and incentives earned | Complex calculation based on sales tiers | Employee compensation |

### Status Codes

#### Employee Status
- `1`: Active employee
- `0`: Terminated employee

#### Member Status
- `1`: Active member
- `0`: Inactive/Cancelled member

#### Agreement Status
- `Posted`: Processed agreement
- `Approval`: Pending approval
- `null`: Standard processing

### Earnings Codes
- **Standard Codes**: Numeric codes (e.g., 1001, 2005)
- **Tanning Codes**: Prefixed with 'T' (e.g., T1001)
- **Excluded Codes**: Prefixed with 'X' (e.g., X1001)

## Business Rules & Logic

### Membership Counting Rules
1. **Active Member Criteria**:
   - Member status = 1 (Active) OR
   - Cancellation date > 30 days from sign date

2. **Exclusion Criteria**:
   - Employee memberships (pp.Employee = 1)
   - Upgraded memberships (pp.upgraded = 1)
   - Downgraded memberships (pp.downgraded = 1)
   - Corporate plans (CorpPlan = 1)

3. **Date Range Logic**:
   - Daily reports: Single day (00:00:00 to 23:59:59)
   - MTD reports: First day of month to current date
   - Historical: Custom date ranges with inclusive boundaries

### Reward Calculations
```php
// Reward calculation logic
$reward = 0;
if ($employment_employeeStatus == 1) { // Active employee
    if (substr($employment_earningsCode, 0, 1) == 'T') {
        // Tanning employee - only tanning rewards
        $reward = $TanningReward;
    } elseif (substr($employment_earningsCode, 0, 1) == 'X') {
        // Excluded employee - no rewards
        $reward = 0;
    } else {
        // Standard employee - all applicable rewards
        $reward = $TanningReward + $UpgradeReward + $EFTRewards + $C1K10Reward;
    }
}
```

### Performance Metrics
- **6-Month Average**: Rolling average of last 6 complete months
- **MTD Comparison**: Current month vs. same period last month
- **Goal Tracking**: Performance vs. configured targets
- **Trend Analysis**: Month-over-month growth calculations

## Deployment & Maintenance

### Environment Requirements
- **PHP**: 7.4+ (CodeIgniter 3.x compatibility)
- **MySQL**: 5.7+ or MariaDB 10.2+
- **Web Server**: Apache 2.4+ or Nginx 1.14+
- **Memory**: 512MB+ PHP memory limit
- **Storage**: SSD recommended for database performance

### Deployment Checklist
1. **Database Setup**:
   - Import schema from `_DB/tribe-struct-*.sql`
   - Configure database connections
   - Set up user permissions

2. **Application Configuration**:
   - Update `config/database.php`
   - Configure Ion Auth settings
   - Set up access permissions

3. **File Permissions**:
   - Writable: `application/logs/`, `application/cache/`
   - Readable: All application files
   - Executable: `index.php`

4. **Sync Configuration**:
   - ABC Financial API credentials
   - CRON job setup for data synchronization
   - Error notification configuration

### Maintenance Tasks

#### Daily
- Monitor sync job execution
- Check error logs for issues
- Verify data accuracy

#### Weekly
- Database performance review
- User access audit
- Backup verification

#### Monthly
- Performance optimization review
- Security updates
- Feature usage analysis

### Backup Strategy
- **Database**: Daily automated backups with 30-day retention
- **Application Files**: Weekly backups of custom code
- **Configuration**: Version-controlled configuration files
- **Recovery Testing**: Monthly restore procedure validation

## Security Considerations

### Data Protection
- **Sensitive Data**: Employee personal information, sales performance
- **Access Logging**: Track user access to sensitive reports
- **Data Retention**: Comply with company data retention policies
- **Encryption**: Database encryption for sensitive fields

### Authentication Security
- **Session Management**: Secure session handling via Ion Auth
- **Password Policies**: Enforce strong password requirements
- **Multi-factor Authentication**: Optional 2FA for admin users
- **Account Lockout**: Prevent brute force attacks

### Authorization Controls
- **Role-based Access**: Hierarchical permission system
- **Principle of Least Privilege**: Minimal required access
- **Regular Audits**: Quarterly access review
- **Separation of Duties**: Different roles for different functions

## Performance Monitoring

### Key Performance Indicators
- **Page Load Time**: Target < 3 seconds
- **Database Query Time**: Target < 500ms per query
- **Concurrent Users**: Support 50+ simultaneous users
- **Data Freshness**: Sync lag < 15 minutes

### Monitoring Tools
```php
// Performance logging
class PerformanceMonitor {
    public static function logQuery($query, $execution_time) {
        if ($execution_time > 0.5) { // Log slow queries
            log_message('warning', "Slow Query ({$execution_time}s): {$query}");
        }
    }

    public static function logPageLoad($page, $load_time) {
        log_message('info', "Page Load: {$page} - {$load_time}s");
    }
}
```

### Optimization Strategies
1. **Database Indexing**: Strategic index placement
2. **Query Optimization**: Efficient JOIN operations
3. **Caching**: Implement result caching for static data
4. **CDN**: Content delivery for static assets
5. **Compression**: Enable gzip compression

## Conclusion

The Team Member Dashboard is a critical business intelligence tool that provides comprehensive insights into sales performance and team productivity. Its robust architecture, security features, and extensive customization options make it an essential component of the fitness club management system.

### Key Strengths
- **Comprehensive Metrics**: Complete performance tracking
- **Real-time Data**: Up-to-date information for decision making
- **Flexible Filtering**: Customizable views and date ranges
- **Secure Access**: Role-based permissions and audit trails
- **Scalable Design**: Supports multiple clubs and large datasets

### Success Metrics
- **User Adoption**: 95%+ of eligible users actively using dashboard
- **Data Accuracy**: 99.9%+ accuracy in sales reporting
- **Performance**: Sub-3-second page load times
- **Availability**: 99.5%+ uptime excluding maintenance windows

This documentation serves as a complete reference for developers, administrators, and business users working with the Team Member Dashboard system.

---

## Data Flow & Table Population

### Overview of Data Sources

The Team Member Dashboard relies on a complex data pipeline that transforms raw external data into actionable business intelligence. The main database tables are populated through a multi-stage process involving API synchronization, data transformation, and KPI calculations.

### Primary Data Sources

#### 1. ABC Financial API
- **Source**: External fitness management system
- **Data Types**: Members, employees, agreements, transactions, events
- **Sync Frequency**: Multiple times daily via CRON jobs
- **Authentication**: API credentials with SSL certificates

#### 2. Point of Sale (POS) Systems
- **Source**: Club transaction systems
- **Data Types**: Sales transactions, tanning services, personal training
- **Integration**: Real-time transaction feeds

#### 3. FTP File Transfers
- **Source**: Various external systems
- **Data Types**: Member status updates, billing information
- **Processing**: Automated file parsing and data extraction

### Data Pipeline Architecture

```mermaid
graph TD
    A[ABC Financial API] --> B[Raw Data Tables]
    C[POS Systems] --> B
    D[FTP Files] --> B
    B --> E[Data Processing Layer]
    E --> F[Normalized Tables]
    F --> G[KPI Calculation Engine]
    G --> H[Dashboard Tables]
    H --> I[Team Member Dashboard]
```

## Main Table Population Process

### 1. abc_employees Table - Employee Data Pipeline

#### STEP 1: CRON Job Triggers Employee Sync
```bash
# Every 4 hours via fetch_remote_employees_cli.sh
0 */4 * * * /path/to/CRONs/fetch_remote_employees_cli.sh

# This executes:
php index.php services/abcfinancial/fetch_all_remote_employees
```

#### STEP 2: ABC Financial API Call
```php
// Controller: services/Abcfinancial.php -> fetch_all_remote_employees()
// For each club, call ABC Financial API:
$arrRemoteEmployees = $this->abcfinancial_API_model->getRemoteEmployeesData(
    $strClubId,                    // Club ID from ABC Financial
    $arrFetchPeriod['period_start'], // Date range start
    $arrFetchPeriod['period_end']    // Date range end
);
```

#### STEP 3: Raw Data Storage (abc_RAW_employees)
```php
// Model: Abcfinancial_employees_model.php -> insertRawEmployeeData()
foreach ($arrRemoteEmployees as $arrOneRemoteEmployee) {
    // Store raw JSON data from API
    $this->db->insert('abc_RAW_employees', [
        'employees_sync_id' => $intSyncId,
        'club_id_local' => $intClubId,
        'employeeId' => $arrOneRemoteEmployee['employeeId'],
        'personal_firstName' => $arrOneRemoteEmployee['personal']['firstName'],
        'personal_lastName' => $arrOneRemoteEmployee['personal']['lastName'],
        'employment_employeeStatus' => $arrOneRemoteEmployee['employment']['employeeStatus'],
        'employment_startDate' => $arrOneRemoteEmployee['employment']['startDate'],
        // ... all other raw fields from API
        'created' => time(),
        'updated' => time()
    ]);
}
```

#### STEP 4: Data Processing & Transformation
```php
// Model: Abcfinancial_employees_model.php -> processFetchedEmployeeData()
$arrRawEmployees = $this->findRawEmployeesOfSync($intSyncId, $intClubId);

foreach ($arrRawEmployees as $arrOneRawData) {
    // Transform raw data to proper format
    $arrEmployeeDataProcessed = $this->processRawEmployeeData($arrOneRawData);

    // Key transformations:
    // - Convert date strings to MySQL date format
    // - Convert 'active'/'inactive' to 1/0
    // - Parse wage as integer
    // - Clean and validate field formats
}
```

#### STEP 5: Final Storage (abc_employees)
```php
// Check if employee already exists
$arrLocalEmployeeData = $this->getEmployeeByRemoteId($arrOneRawData['employeeId']);

if (!empty($arrLocalEmployeeData)) {
    // UPDATE existing employee
    $this->db->where('id', $arrLocalEmployeeData['id']);
    $this->db->update('abc_employees', $arrEmployeeDataProcessed);
} else {
    // INSERT new employee
    $arrEmployeeDataProcessed['created'] = time();
    $this->db->insert('abc_employees', $arrEmployeeDataProcessed);
}
```

#### Final abc_employees Table Structure
- `employeeIdRemote`: ABC Financial employee ID (primary key for lookups)
- `personal_firstName/lastName`: Employee names for dashboard display
- `employment_employeeStatus`: 1=Active, 0=Terminated (filters active employees)
- `employment_earningsCode`: Commission structure classification
- `employment_startDate/terminationDate`: Employment period dates
- `club_id_local`: Links employee to specific club location

### 2. abc_members_agreements Table - Member Agreement Data Pipeline

#### STEP 1: CRON Job Triggers Member/Agreement Sync
```bash
# Every 2 hours via fetch_remote_members_segmented.sh
0 */2 * * * /path/to/CRONs/fetch_remote_members_segmented.sh

# This executes segmented fetching:
php index.php services/abcfinancial/fetch_all_remote_members_and_agreements_by_segment
```

#### STEP 2: ABC Financial API Call for Members & Agreements
```php
// Controller: services/Abcfinancial.php -> fetch_all_remote_members_and_agreements_by_segment()
// For each club, fetch member and agreement data:
$arrRemoteMembersAndAgreements = $this->abcfinancial_API_model
    ->getRemoteMembersWithPersonalAndAgreementData(
        $strClubId,                    // Club ID
        $arrFetchPeriod['period_start'], // Date range start
        $arrFetchPeriod['period_end']    // Date range end
    );
```

#### STEP 3: Raw Data Storage (abc_RAW_members_agreements)
```php
// Model: Abcfinancial_members_agreement_model.php
foreach ($arrRemoteMembersAndAgreements as $arrOneMemberAndAgreement) {
    // Store raw agreement data from API
    $this->db->insert('abc_RAW_members_agreements', [
        'members_agreements_sync_id' => $intSyncId,
        'club_id' => $intClubId,
        'member_id' => $arrOneMemberAndAgreement['memberId'],
        'agreementNumber' => $arrOneMemberAndAgreement['agreement']['agreementNumber'],
        'membershipType' => $arrOneMemberAndAgreement['agreement']['membershipType'],
        'salesPersonId' => $arrOneMemberAndAgreement['agreement']['salesPersonId'],
        'salesPersonName' => $arrOneMemberAndAgreement['agreement']['salesPersonName'],
        'signDate' => $arrOneMemberAndAgreement['agreement']['signDate'],
        'paymentPlan_id' => $arrOneMemberAndAgreement['agreement']['paymentPlan']['id'],
        // ... all other raw agreement fields
        'created' => time(),
        'updated' => time()
    ]);
}
```

#### STEP 4: Data Processing & Normalization
```php
// Model: Abcfinancial_members_agreement_model.php -> updateMemberAgreementDataFromRaw()
$arrRawAgreements = $this->findRawTransactionsOfSyncAgreements($intSyncId, $intClubId);

foreach ($arrRawAgreements as $arrOneRawAgreement) {
    // Transform and validate agreement data
    $arrAgreementProcessed = $this->processRawAgreementData($arrOneRawAgreement);

    // Key transformations:
    // - Convert Unix timestamps to MySQL datetime
    // - Validate payment plan relationships
    // - Clean membership type names
    // - Parse financial amounts
}
```

#### STEP 5: Final Storage (abc_members_agreements)
```php
// Check if agreement already exists
$arrExistingAgreement = $this->getMemberAgreementByRemoteId($agreementId);

if (empty($arrExistingAgreement)) {
    // INSERT new agreement
    $this->db->insert('abc_members_agreements', $arrAgreementProcessed);
} else {
    // UPDATE existing agreement
    $this->db->where('id', $arrExistingAgreement['id']);
    $this->db->update('abc_members_agreements', $arrAgreementProcessed);
}
```

#### Final abc_members_agreements Table Structure
- `agreementNumber`: Unique agreement identifier from ABC Financial
- `member_id`: Links to abc_members table
- `membershipType`: Base, Peak, Peak Plus, Peak Results, etc.
- `salesPersonId`: Employee who sold the membership (links to abc_employees)
- `salesPersonName`: Employee name for dashboard display
- `signDate`: When membership was sold (key for KPI calculations)
- `paymentPlan_id`: Links to abc_payment_plans for pricing/commission info
- `club_id`: Links agreement to specific club location

### 3. udt_agreements Table - Dashboard-Ready Agreement Data

#### STEP 1: CRON Job Triggers Change History Processing
```bash
# Daily via update_change_history.sh (part of batch_run_all.sh)
sh ./KPI/update_change_history.sh

# This executes:
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME < change_history.sql
```

#### STEP 2: Data Transformation from abc_members_agreements
```sql
-- From CRONs/KPI/change_history.sql
-- This SQL script runs multiple INSERT statements to populate udt_agreements

-- Insert NEW membership agreements (never processed before)
INSERT INTO udt_agreements (
    club_id, agreement_id, agreement_lastupdated, member_id, Date,
    agreementNumber, firstname, lastname, membershipType,
    sinceDate, signDate, convertedDate, lastRewriteDate, beginDate,
    salesPersonId, salesPersonName, paymentPlanId, paymentPlan_id, PaymentPlan,
    campaignName, agreementPaymentMethod, downPayment,
    firstPaymentDate, nextBillingDate, nextDueAmount, status_id, Status
)
SELECT
    c.id as club_id,                    -- Club ID for filtering
    a.id AS agreement_id,               -- Links back to source agreement
    FROM_UNIXTIME(a.updated),           -- Last update timestamp
    a.member_id,                        -- Member identifier
    a.signDate AS Date,                 -- Sale date (KEY for KPI calculations)
    a.agreementNumber,                  -- Agreement number for tracking
    p.firstname, p.lastname,            -- Member names for display
    a.membershipType,                   -- Base/Peak/Peak Results (KEY for KPI)
    a.sinceDate, a.signDate, a.convertedDate, a.lastRewriteDate, a.beginDate,
    a.salesPersonId,                    -- Employee ID (KEY for dashboard filtering)
    a.salesPersonName,                  -- Employee name for display
    a.paymentPlan_id,                   -- Payment plan ID
    a.paymentPlan_id,                   -- Duplicate for legacy reasons
    pp.name AS PaymentPlan,             -- Payment plan name
    a.campaignName,                     -- Marketing campaign
    a.agreementPaymentMethod,           -- EFT/CC/Cash payment method
    a.downPayment,                      -- Down payment amount
    a.firstPaymentDate, a.nextBillingDate, a.nextDueAmount,
    ms.id AS status_id,                 -- Member status ID
    ms.name AS Status                   -- Member status name
FROM abc_members_agreements a
LEFT JOIN abc_members_personaldata p ON a.member_id = p.member_id
LEFT JOIN abc_member_statuses ms ON ms.id = p.memberStatus_id
LEFT JOIN abc_clubs c ON c.id = a.club_id
LEFT JOIN abc_payment_plans pp ON pp.id = a.paymentPlan_id
LEFT JOIN udt_agreements aa ON aa.agreement_id = a.id
WHERE a.membershipType <> 'Prospect'                    -- Exclude prospects
  AND FROM_UNIXTIME(a.created) > @PriorUpdate          -- Only new agreements
  AND FROM_UNIXTIME(a.created) <= @LastProcess         -- Within processing window
  AND aa.agreement_id IS NULL                           -- Not already processed
GROUP BY a.id, member_id, club_id;
```

#### STEP 3: Additional Change History Processing
```sql
-- The change_history.sql script also processes:

-- 1. Agreement upgrades/downgrades
INSERT INTO udt_agreement_changes (...)
SELECT ... WHERE Upgraded=1 OR Downgraded=1;

-- 2. Member status changes (active/cancelled/frozen)
INSERT INTO udt_agreement_changes (...)
SELECT ... WHERE memberStatusDate changed;

-- 3. Payment method changes
INSERT INTO udt_agreement_changes (...)
SELECT ... WHERE agreementPaymentMethod changed;
```

#### Purpose & Function of udt_agreements
- **Denormalized Structure**: All agreement data pre-joined for fast queries
- **Dashboard Optimization**: No complex JOINs needed in dashboard queries
- **Historical Snapshot**: Captures agreement state at time of processing
- **KPI Ready**: Structured exactly as needed for Team Member Dashboard
- **Performance**: Indexed on SalesPersonId, club_id, Date for fast filtering

### 4. monthly_tm_kpis Table - Final KPI Calculations

#### STEP 1: CRON Job Triggers Daily KPI Processing
```bash
# Daily at 2:00 AM via update_daily_kpi_data_script_1.sh
0 2 * * * /path/to/CRONs/KPI/update_daily_kpi_data_script_1.sh

# This executes the massive KPI calculation:
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME < daily_kpis.sql
```

#### STEP 2: Intermediate Table Population (monthly_tm_kpis_details)
```sql
-- From CRONs/KPI/daily_kpis.sql (line ~2558)
-- First, populate detailed records for each agreement
INSERT INTO monthly_tm_kpis_details
SELECT
    @MonthEndDate AS MonthEndDate,
    c.id AS club_id,
    c.name as Club,
    a.SalesPersonId,                    -- Employee who made the sale
    a.SalesPersonName,
    a.Date,                             -- Sale date
    a.agreementNumber,
    p.firstName, p.lastName,
    a.membershipType,                   -- Base/Peak/Peak Results
    PaymentPlan,
    ma.agreementPaymentMethod,          -- EFT/CC/Cash
    pp.EFT,                            -- 1 if EFT payment plan

    -- REWARD CALCULATION (key business logic)
    IF(a.salesPersonId <> '',
        IF(a.signDate <= InPresale, pp.psreward,    -- Pre-sale reward
            IF(a.signDate>='2024-04-01' AND a.SignDate<='2024-06-30' AND pp.reward=2, 3,
                IF(a.signDate>='2024-03-01' AND a.SignDate<='2024-06-30' AND pp.reward=3, 5,
                    pp.reward                        -- Standard reward
                )
            )
        ), 0
    ) AS reward,

    -- MEMBERSHIP TYPE COUNTING (key for dashboard totals)
    IF(a.membershipType LIKE 'Base%', 1, 0) AS Base,
    IF(a.membershipType IN ('Peak','PEAK CORP'), 1, 0) AS Peak,
    IF(a.membershipType = 'Peak Plus', 1, 0) AS PeakPlus,
    IF(a.membershipType IN ('Peak Results','PEAK RESULTS CORP'), 1, 0) AS PeakResults,

    pp.price,                           -- Monthly dues amount
    pp.id AS ccid                       -- Payment plan ID
FROM udt_agreements a                   -- Source: dashboard-ready agreements
LEFT JOIN abc_members_personaldata p ON a.member_id = p.member_id
LEFT JOIN abc_clubs c ON c.id = a.club_id
LEFT JOIN abc_payment_plans pp ON pp.id = a.paymentPlan_id
LEFT JOIN abc_members_agreements ma ON ma.id = a.agreement_id
WHERE a.Date >= @MonthStartDateTime     -- Filter by date range
  AND a.Date <= @EndDayTimeEnd
  AND a.salesPersonId <> ''             -- Must have salesperson
  AND pp.employee = 0                   -- Exclude employee memberships
  AND pp.corporate = 0;                 -- Exclude corporate memberships
```

#### STEP 3: Final KPI Aggregation (monthly_tm_kpis)
```sql
-- From CRONs/KPI/daily_kpis.sql (line ~2046)
-- Aggregate detailed records into final KPI summary
INSERT INTO monthly_tm_kpis (
    SalesPersonId, SalesPersonName, club_id, Base, Peak, PeakPlus, PeakResults,
    AverageDues, PeaksPercent, EFTRewards, SecondaryBilling, checkingPercent,
    TotalCH, FromDate, ToDate
)
SELECT
    SalesPersonId,                      -- Employee ID (key for dashboard filtering)
    SalesPersonName,                    -- Employee name for display
    club_id,                           -- Club ID for filtering

    -- MEMBERSHIP COUNTS (core dashboard metrics)
    SUM(Base) AS Base,                 -- Count of Base memberships
    SUM(Peak) AS Peak,                 -- Count of Peak memberships
    SUM(PeakPlus) AS PeakPlus,         -- Count of Peak Plus memberships
    SUM(PeakResults) AS PeakResults,   -- Count of Peak Results memberships

    -- FINANCIAL CALCULATIONS
    ROUND(SUM((IF(EFT=1 AND ccid IS NOT NULL,1,0)*price))/
          SUM(IF(EFT=1 AND ccid IS NOT NULL,1,0)), 2) AS AverageDues,

    -- PERCENTAGE CALCULATIONS
    (COUNT(agreement_id)-SUM(Base))/COUNT(agreement_id) AS PeaksPercent,

    -- REWARD TOTALS
    SUM(reward) AS EFTRewards,         -- Total commission earned

    -- PAYMENT METHOD PERCENTAGES
    ROUND((SUM(IF(agreementPaymentMethod='EFT' AND ccid IS NOT NULL,1,0))/
           COUNT(SalesPersonId))*100,2) AS SecondaryBilling,
    ROUND((SUM(IF(agreementPaymentMethod = 'EFT' AND EFT=1, 1, 0))/
           COUNT(SalesPersonId))*100,2) AS checkingPercent,

    @MonthStartDateTime AS FromDate,    -- Period start
    @EndDayTimeEnd AS ToDate           -- Period end
FROM monthly_tm_kpis_details
WHERE MonthEndDate = @MonthEndDate
GROUP BY SalesPersonId, club_id;       -- One record per employee per club
```

#### STEP 4: Additional Revenue Calculations (POS Transactions)
```sql
-- TANNING SALES REWARDS (10% commission)
-- From CRONs/KPI/daily_kpis.sql (line ~2243)
INSERT INTO monthly_tm_kpis (SalespersonId, salesPersonName, club_id, TanningSales, TanningReward)
SELECT
    e.employeeIdRemote AS SalespersonId,
    CONCAT(personal_firstName, ' ', personal_lastName) AS salesPersonName,
    t.club_id_local AS club_id,
    ROUND(SUM(subtotal), 2) AS Tanning,
    ROUND(SUM(subtotal) * 0.1, 2) AS TanningReward    -- 10% commission
FROM abc_pos_transactions t
LEFT JOIN abc_pos_transaction_items p ON p.pos_transaction_id = t.id
LEFT JOIN abc_employees e ON t.employeeID = e.employeeIdRemote
WHERE transactiontimestamp >= @MonthStartDateTime
  AND transactiontimestamp <= @EndDayTimeEnd
  AND p.itemName LIKE '%tan%'                         -- Tanning services only
GROUP BY e.employeeIdRemote, t.club_id_local;

-- C1K CONSULTATION REWARDS ($5 per consultation)
-- From CRONs/KPI/daily_kpis.sql (line ~2330)
INSERT INTO monthly_tm_kpis (SalespersonId, salesPersonName, club_id, C1K10, C1K10Reward)
SELECT
    t.employeeID as SalespersonId,
    CONCAT(personal_firstName, ' ', personal_lastName) AS salesPersonName,
    t.club_id_local AS club_id,
    SUM(IF(unitPrice=10,1,0)) AS C1K10,               -- Count of $10 consultations
    (SUM(IF(unitPrice=10,1,0)) * 5) AS C1K10Reward   -- $5 per consultation
FROM abc_pos_transactions t
LEFT JOIN abc_pos_transaction_items p ON p.pos_transaction_id = t.id
LEFT JOIN abc_employees e ON t.employeeID = e.employeeIdRemote
WHERE transactiontimestamp >= @MonthStartDateTime
  AND transactiontimestamp <= @EndDayTimeEnd
  AND p.itemName LIKE '%C1K%'                        -- C1K consultations only
GROUP BY t.employeeID, t.club_id_local;
```

#### Final monthly_tm_kpis Table Structure
- **SalesPersonId**: Employee ID (links to abc_employees.employeeIdRemote)
- **SalesPersonName**: Employee name for dashboard display
- **club_id**: Club location identifier
- **Base/Peak/PeakPlus/PeakResults**: Membership counts by type
- **AverageDues**: Average monthly dues for EFT memberships
- **PeaksPercent**: Percentage of non-Base memberships
- **EFTRewards**: Total commission from membership sales
- **TanningSales/TanningReward**: Tanning revenue and 10% commission
- **C1K10/C1K10Reward**: Consultation count and $5 per consultation
- **FromDate/ToDate**: Date range for the KPI period

#### Key Business Logic in KPI Calculations
1. **Membership Exclusions**: Employee and corporate memberships excluded
2. **Reward Tiers**: Different commission rates for pre-sale vs regular periods
3. **EFT Focus**: Average dues calculated only for EFT payment plans
4. **Date Filtering**: Only agreements within specified date range counted
5. **Salesperson Requirement**: Only agreements with assigned salesperson included

## CRON Job Schedule & Data Flow

### Daily Data Synchronization

#### Primary Sync Jobs
```bash
# Employee Data Sync - Every 4 hours
0 */4 * * * /path/to/CRONs/fetch_remote_employees_cli.sh

# Member Data Sync - Every 2 hours
0 */2 * * * /path/to/CRONs/fetch_remote_members_segmented.sh

# Event Data Sync - Every 6 hours
0 */6 * * * /path/to/CRONs/fetch_remote_events.sh

# Check-in Data Sync - Every hour
0 * * * * /path/to/CRONs/fetch_remote_checkins.sh
```

#### KPI Processing Jobs
```bash
# Daily KPI Calculation - 2:00 AM
0 2 * * * /path/to/CRONs/KPI/daily_kpi_cron.sh

# Secondary KPI Processing - 3:00 AM
0 3 * * * /path/to/CRONs/KPI/update_daily_kpi_data_script_2.sh

# Final KPI Updates - 4:00 AM
0 4 * * * /path/to/CRONs/KPI/update_daily_kpi_data_script_3.sh
```

### Data Processing Workflow

#### Stage 1: Raw Data Collection
1. **API Calls**: Fetch data from ABC Financial
2. **Raw Storage**: Store in `abc_RAW_*` tables
3. **Validation**: Check data integrity and completeness
4. **Error Handling**: Log failures and retry mechanisms

#### Stage 2: Data Normalization
1. **Type Conversion**: Convert dates, numbers, booleans
2. **Relationship Mapping**: Link foreign keys
3. **Data Cleaning**: Remove duplicates, fix inconsistencies
4. **Normalized Storage**: Insert into main tables

#### Stage 3: KPI Calculation
1. **Agreement Processing**: Transform agreements to `udt_agreements`
2. **Membership Counting**: Calculate Base/Peak/Peak Results
3. **Revenue Calculations**: Average dues, rewards, commissions
4. **Performance Metrics**: Percentages, ratios, trends

#### Stage 4: Dashboard Preparation
1. **Aggregation**: Summarize data by team member and time period
2. **Historical Analysis**: Calculate 6-month averages
3. **Goal Comparison**: Compare against targets
4. **Final Storage**: Populate `monthly_tm_kpis` table

## Data Quality & Monitoring

### Sync Status Tracking

#### Sync Tables
```sql
-- Employee Sync Status
CREATE TABLE `abc_sync_employees` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `club_id` int(10) unsigned DEFAULT '0',
  `fetched_items_count` int(10) unsigned NOT NULL DEFAULT '0',
  `status` enum('new','processing','failed','success','processed','processing_failed'),
  `finished_at` int(10) unsigned NOT NULL DEFAULT '0',
  `created` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`)
);

-- Member Agreement Sync Status
CREATE TABLE `abc_sync_members_agreements` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `club_id` int(10) unsigned DEFAULT '0',
  `period_start` datetime DEFAULT NULL,
  `period_end` datetime DEFAULT NULL,
  `fetched_items_count` int(10) unsigned NOT NULL DEFAULT '0',
  `status` enum('new','processing','failed','success','processed'),
  `finished_at` int(10) unsigned NOT NULL DEFAULT '0',
  `api_session_id` int(10) unsigned DEFAULT '0',
  `created` int(10) unsigned NOT NULL,
  PRIMARY KEY (`id`)
);
```

#### Status Monitoring
```php
// Check last successful sync
public function getLastSuccessfulProcessingMembers($intClubId) {
    $query = "
        SELECT MAX(finished_at) as last_sync
        FROM abc_sync_members_agreements
        WHERE club_id = ?
          AND status = 'success'
          AND finished_at > 0
    ";

    $result = $this->db->query($query, [$intClubId])->row_array();
    return $result ? date('Y-m-d H:i:s', $result['last_sync']) : 'Never';
}
```

### Data Validation Rules

#### Employee Data Validation
- **Required Fields**: employeeIdRemote, firstName, lastName
- **Status Validation**: employeeStatus must be 0 or 1
- **Date Validation**: startDate <= terminationDate (if terminated)
- **Earnings Code**: Must match predefined patterns

#### Agreement Data Validation
- **Member Linkage**: Valid member_id must exist
- **Club Linkage**: Valid club_id must exist
- **Date Logic**: signDate <= sinceDate <= lastRewriteDate
- **Payment Plan**: Valid paymentPlan_id must exist
- **Status Consistency**: Member status aligns with agreement status

#### KPI Data Validation
- **Calculation Accuracy**: Sum of Base+Peak+PeakResults = TotalMems
- **Percentage Bounds**: All percentages between 0-100
- **Revenue Logic**: AverageDues > 0 for active memberships
- **Date Consistency**: FromDate <= ToDate

### Error Handling & Recovery

#### Sync Failure Recovery
```php
// Retry failed syncs
public function retryFailedSyncs($maxRetries = 3) {
    $failedSyncs = $this->db->query("
        SELECT id, club_id, status
        FROM abc_sync_members_agreements
        WHERE status IN ('failed', 'processing_failed')
          AND retry_count < ?
    ", [$maxRetries])->result_array();

    foreach ($failedSyncs as $sync) {
        $this->reprocessSync($sync['id']);
    }
}
```

#### Data Integrity Checks
```sql
-- Check for orphaned agreements
SELECT COUNT(*) as orphaned_agreements
FROM abc_members_agreements a
LEFT JOIN abc_members m ON m.id = a.member_id
WHERE m.id IS NULL;

-- Check for missing employee data
SELECT COUNT(*) as missing_employees
FROM monthly_tm_kpis k
LEFT JOIN abc_employees e ON e.employeeIdRemote = k.SalesPersonId
WHERE e.id IS NULL;

-- Validate KPI calculations
SELECT
    SalesPersonId,
    (Base + Peak + PeakResults) as calculated_total,
    TotalMems as stored_total,
    ABS((Base + Peak + PeakResults) - TotalMems) as difference
FROM monthly_tm_kpis
WHERE ABS((Base + Peak + PeakResults) - TotalMems) > 0;
```

### Performance Optimization

#### Database Indexing Strategy
```sql
-- Employee table indexes
ALTER TABLE abc_employees ADD INDEX idx_employeeIdRemote (employeeIdRemote);
ALTER TABLE abc_employees ADD INDEX idx_club_status (club_id_local, employment_employeeStatus);

-- Agreement table indexes
ALTER TABLE abc_members_agreements ADD INDEX idx_salesperson_date (salesPersonId, signDate);
ALTER TABLE abc_members_agreements ADD INDEX idx_club_membership (club_id, membershipType);

-- KPI table indexes
ALTER TABLE monthly_tm_kpis ADD INDEX idx_salesperson_period (SalesPersonId, FromDate, ToDate);
ALTER TABLE monthly_tm_kpis ADD INDEX idx_club_period (club_id, FromDate, ToDate);

-- UDT agreements indexes
ALTER TABLE udt_agreements ADD INDEX idx_salesperson_date (SalesPersonId, Date);
ALTER TABLE udt_agreements ADD INDEX idx_club_date (club_id, Date);
```

#### Query Optimization
```sql
-- Optimized team member data query
SELECT
    sp.SalesPersonId,
    sp.SalesPersonName,
    sp.club_id,
    COALESCE(sp.Base, 0) as Base,
    COALESCE(sp.Peak, 0) as Peak,
    COALESCE(sp.PeakResults, 0) as PeakResults,
    COALESCE(sp.TotalMems, 0) as TotalMems,
    COALESCE(sp.AverageDues, 0.00) as AverageDues
FROM (
    SELECT DISTINCT SalesPersonId, SalesPersonName, club_id
    FROM udt_agreements
    WHERE Date >= ? AND Date <= ?
      AND club_id = ?
) sp
LEFT JOIN monthly_tm_kpis k ON k.SalesPersonId = sp.SalesPersonId
                            AND k.club_id = sp.club_id
                            AND k.FromDate <= ?
                            AND k.ToDate >= ?
ORDER BY sp.SalesPersonName;
```

### Monitoring & Alerting

#### Key Metrics to Monitor
- **Sync Success Rate**: Percentage of successful API calls
- **Data Freshness**: Time since last successful sync
- **Processing Time**: Duration of KPI calculations
- **Error Frequency**: Rate of sync failures and data errors
- **Data Volume**: Number of records processed per sync

#### Alert Conditions
```php
// Critical alerts
if ($syncFailureRate > 10) {
    $this->sendAlert('CRITICAL: High sync failure rate');
}

if ($dataAge > 4 * 3600) { // 4 hours
    $this->sendAlert('WARNING: Stale data detected');
}

if ($kpiProcessingTime > 1800) { // 30 minutes
    $this->sendAlert('WARNING: Slow KPI processing');
}

// Data quality alerts
if ($orphanedRecords > 100) {
    $this->sendAlert('DATA QUALITY: High orphaned record count');
}
```

#### Notification System
```php
// Email notifications for sync completion
public function sendSyncNotification($syncType, $status, $details) {
    $subject = "Data Sync {$status}: {$syncType}";
    $message = "
        Sync Type: {$syncType}
        Status: {$status}
        Records Processed: {$details['record_count']}
        Processing Time: {$details['duration']}
        Errors: {$details['error_count']}
    ";

    $this->email->to($this->config->item('admin_emails'));
    $this->email->subject($subject);
    $this->email->message($message);
    $this->email->send();
}
```

## Troubleshooting Data Issues

### Common Data Problems

#### 1. Missing Team Member Data
**Symptoms**: Team members not appearing in dashboard
**Diagnosis**:
```sql
-- Check employee sync status
SELECT * FROM abc_sync_employees
WHERE status != 'success'
ORDER BY created DESC LIMIT 10;

-- Check for missing employee records
SELECT DISTINCT SalesPersonId, SalesPersonName
FROM udt_agreements
WHERE SalesPersonId NOT IN (
    SELECT employeeIdRemote FROM abc_employees
);
```
**Resolution**: Re-run employee sync, verify API credentials

#### 2. Incorrect KPI Calculations
**Symptoms**: Dashboard totals don't match expected values
**Diagnosis**:
```sql
-- Compare raw agreement counts vs KPI totals
SELECT
    SalesPersonId,
    COUNT(*) as raw_agreements,
    SUM(CASE WHEN membershipType LIKE 'Base%' THEN 1 ELSE 0 END) as raw_base,
    k.Base as kpi_base,
    k.TotalMems as kpi_total
FROM udt_agreements u
LEFT JOIN monthly_tm_kpis k ON k.SalesPersonId = u.SalesPersonId
WHERE u.Date >= '2024-01-01'
GROUP BY SalesPersonId
HAVING raw_agreements != kpi_total;
```
**Resolution**: Re-run KPI calculations, check date filters

#### 3. Sync Performance Issues
**Symptoms**: Slow dashboard loading, timeouts
**Diagnosis**: Check query execution times, database locks
**Resolution**: Optimize indexes, implement query caching

### Data Recovery Procedures

#### 1. Restore from Backup
```bash
# Restore specific table from backup
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME < backup_monthly_tm_kpis.sql

# Verify data integrity after restore
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
    SELECT COUNT(*) as record_count,
           MAX(ToDate) as latest_date
    FROM monthly_tm_kpis;"
```

#### 2. Rebuild KPI Data
```bash
# Clear existing KPI data for date range
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME -e "
    DELETE FROM monthly_tm_kpis
    WHERE FromDate >= '2024-01-01'
      AND ToDate <= '2024-01-31';"

# Re-run KPI calculations
mysql -h $DB_HOST -u $DB_USER -p$DB_PASS $DB_NAME < daily_kpis.sql
```

#### 3. Manual Data Correction
```sql
-- Fix specific employee data
UPDATE abc_employees
SET employment_employeeStatus = 1
WHERE employeeIdRemote = 'EMP123'
  AND employment_terminationDate IS NULL;

-- Recalculate specific KPI record
UPDATE monthly_tm_kpis
SET TotalMems = Base + Peak + PeakResults,
    PeaksPercent = (Peak + PeakResults) / (Base + Peak + PeakResults) * 100
WHERE SalesPersonId = 'EMP123'
  AND FromDate = '2024-01-01';
```

This comprehensive data flow documentation explains exactly how the main database tables get populated, from initial API calls through final KPI calculations, providing complete visibility into the data pipeline that powers the Team Member Dashboard.

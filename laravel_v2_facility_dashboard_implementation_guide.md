# Laravel V2 Facility Dashboard Implementation Guide

## Overview
This document provides comprehensive guidance for implementing the facility dashboard feature in Laravel V2, utilizing the new `abc_tags` table structure for location/market/region data instead of the legacy `abc_club_settings` approach.

## Key Changes in V2
- **Location Data**: Market and region information now stored in `abc_tags` table
- **Woven API Integration**: Already implemented with sync for assets, work orders, employees, and location audits
- **Database Structure**: Migrated from CodeIgniter to Laravel with updated relationships

## Database Schema

### Core Tables

#### 1. `abc_clubs`
```sql
CREATE TABLE `abc_clubs` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL,
  `clubNumber` int(10) unsigned NOT NULL,
  `udt_club_num` int(10) unsigned DEFAULT NULL,
  -- Additional club fields
  PRIMARY KEY (`id`)
);
```

#### 2. `abc_tags` (NEW in V2)
```sql
CREATE TABLE `abc_tags` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `club_id` int(10) unsigned NOT NULL,
  `tag_type` varchar(50) NOT NULL, -- 'market', 'region', 'district'
  `tag_value` varchar(100) NOT NULL, -- 'Market 1', 'Market 2', 'Region A'
  `woven_location_id` varchar(36) DEFAULT NULL, -- Maps to Woven API
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `club_id` (`club_id`),
  KEY `tag_type` (`tag_type`),
  KEY `woven_location_id` (`woven_location_id`),
  FOREIGN KEY (`club_id`) REFERENCES `abc_clubs` (`id`)
);
```

#### 3. `woven_assets`
```sql
CREATE TABLE `woven_assets` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `woven_api_id` varchar(36) NOT NULL,
  `location_id` varchar(36) NOT NULL,
  `asset_type` varchar(10) DEFAULT NULL, -- '1'=Equipment, '2'=Facility
  `asset_tag_number` varchar(50) DEFAULT NULL,
  `asset_name` varchar(200) DEFAULT NULL,
  `asset_category` varchar(100) DEFAULT NULL,
  `manufacturer` varchar(100) DEFAULT NULL,
  `model` varchar(100) DEFAULT NULL,
  `serial_number` varchar(100) DEFAULT NULL,
  `purchase_date` datetime DEFAULT NULL,
  `warranty_expiration_date` datetime DEFAULT NULL,
  `room_number` varchar(50) DEFAULT NULL,
  `display_string_long` varchar(500) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `woven_api_id` (`woven_api_id`),
  KEY `location_id` (`location_id`),
  KEY `asset_type` (`asset_type`)
);
```

#### 4. `woven_workorders`
```sql
CREATE TABLE `woven_workorders` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `woven_api_id` varchar(36) NOT NULL,
  `asset_id` varchar(36) DEFAULT NULL,
  `location_id` varchar(36) DEFAULT NULL,
  `work_order_number` int(10) DEFAULT NULL,
  `work_order_type` smallint(4) DEFAULT NULL, -- '1'=Asset, '2'=Facility
  `work_order_status` smallint(4) DEFAULT NULL, -- '2'=Closed
  `work_order_status_display` varchar(100) DEFAULT NULL,
  `work_order_priority` smallint(4) DEFAULT NULL,
  `work_order_priority_display` varchar(100) DEFAULT NULL,
  `assigned_employee_id` varchar(36) DEFAULT NULL,
  `closed_by_employee_id` varchar(36) DEFAULT NULL,
  `created_on` datetime DEFAULT NULL,
  `closed_date` datetime DEFAULT NULL,
  `is_deleted` tinyint(1) DEFAULT NULL,
  `hours_logged` decimal(5,2) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `woven_api_id` (`woven_api_id`),
  KEY `asset_id` (`asset_id`),
  KEY `location_id` (`location_id`),
  KEY `work_order_status` (`work_order_status`),
  KEY `created_on` (`created_on`)
);
```

#### 5. `woven_employees`
```sql
CREATE TABLE `woven_employees` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT,
  `woven_api_id` varchar(36) NOT NULL,
  `first_name` varchar(50) DEFAULT NULL,
  `last_name` varchar(50) DEFAULT NULL,
  `email` varchar(100) NOT NULL,
  `status` tinyint(1) unsigned DEFAULT '0',
  `company_id` varchar(36) NOT NULL,
  `role_name` varchar(200) NOT NULL,
  `position_name` varchar(200) NOT NULL,
  `hire_date` datetime NOT NULL,
  `primary_location_id` varchar(36) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `email` (`email`),
  UNIQUE KEY `woven_api_id` (`woven_api_id`)
);
```

#### 6. `company_settings`
```sql
CREATE TABLE `company_settings` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` varchar(500) DEFAULT NULL,
  `description` text,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `setting_key` (`setting_key`)
);

-- Key settings for facility dashboard thresholds:
-- FacEqupWorking: Equipment working percentage threshold (high)
-- FacEqupWorkingMid: Equipment working percentage threshold (medium)
-- FacOpenWO: Open work orders threshold (low is good)
-- FacOpenWOMid: Open work orders threshold (medium)
-- FacAging: Work order aging threshold in days (low is good)
-- FacAgingMid: Work order aging threshold in days (medium)
```

## Laravel Models

### 1. Club Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Club extends Model
{
    protected $table = 'abc_clubs';
    
    protected $fillable = [
        'name',
        'clubNumber',
        'udt_club_num'
    ];

    /**
     * Get all tags for this club
     */
    public function tags(): HasMany
    {
        return $this->hasMany(AbcTag::class, 'club_id');
    }

    /**
     * Get market tag for this club
     */
    public function marketTag()
    {
        return $this->tags()->where('tag_type', 'market')->first();
    }

    /**
     * Get region tag for this club
     */
    public function regionTag()
    {
        return $this->tags()->where('tag_type', 'region')->first();
    }

    /**
     * Get district tag for this club
     */
    public function districtTag()
    {
        return $this->tags()->where('tag_type', 'district')->first();
    }

    /**
     * Get assets through woven location mapping
     */
    public function assets(): HasManyThrough
    {
        return $this->hasManyThrough(
            WovenAsset::class,
            AbcTag::class,
            'club_id',           // Foreign key on abc_tags table
            'location_id',       // Foreign key on woven_assets table
            'id',                // Local key on clubs table
            'woven_location_id'  // Local key on abc_tags table
        );
    }

    /**
     * Get work orders through woven location mapping
     */
    public function workOrders(): HasManyThrough
    {
        return $this->hasManyThrough(
            WovenWorkOrder::class,
            AbcTag::class,
            'club_id',           // Foreign key on abc_tags table
            'location_id',       // Foreign key on woven_workorders table
            'id',                // Local key on clubs table
            'woven_location_id'  // Local key on abc_tags table
        );
    }

    /**
     * Get open work orders (status != 2 and not deleted)
     */
    public function openWorkOrders()
    {
        return $this->workOrders()
            ->where('work_order_status', '!=', '2')
            ->where('is_deleted', 0);
    }

    /**
     * Get equipment assets only (asset_type = 1)
     */
    public function equipmentAssets()
    {
        return $this->assets()->where('asset_type', '1');
    }
}
```

### 2. AbcTag Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AbcTag extends Model
{
    protected $table = 'abc_tags';

    protected $fillable = [
        'club_id',
        'tag_type',
        'tag_value',
        'woven_location_id'
    ];

    /**
     * Get the club that owns this tag
     */
    public function club(): BelongsTo
    {
        return $this->belongsTo(Club::class, 'club_id');
    }

    /**
     * Scope for market tags
     */
    public function scopeMarkets($query)
    {
        return $query->where('tag_type', 'market');
    }

    /**
     * Scope for region tags
     */
    public function scopeRegions($query)
    {
        return $query->where('tag_type', 'region');
    }

    /**
     * Scope for district tags
     */
    public function scopeDistricts($query)
    {
        return $query->where('tag_type', 'district');
    }
}
```

### 3. WovenAsset Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WovenAsset extends Model
{
    protected $table = 'woven_assets';

    protected $fillable = [
        'woven_api_id',
        'location_id',
        'asset_type',
        'asset_tag_number',
        'asset_name',
        'asset_category',
        'manufacturer',
        'model',
        'serial_number',
        'purchase_date',
        'warranty_expiration_date',
        'room_number',
        'display_string_long'
    ];

    protected $casts = [
        'purchase_date' => 'datetime',
        'warranty_expiration_date' => 'datetime'
    ];

    /**
     * Get work orders for this asset
     */
    public function workOrders(): HasMany
    {
        return $this->hasMany(WovenWorkOrder::class, 'asset_id', 'woven_api_id');
    }

    /**
     * Get open work orders for this asset
     */
    public function openWorkOrders()
    {
        return $this->workOrders()
            ->where('work_order_status', '!=', '2')
            ->where('is_deleted', 0);
    }

    /**
     * Get club through location mapping
     */
    public function club()
    {
        return $this->belongsToMany(Club::class, 'abc_tags', 'woven_location_id', 'club_id', 'location_id', 'id');
    }

    /**
     * Scope for equipment assets
     */
    public function scopeEquipment($query)
    {
        return $query->where('asset_type', '1');
    }

    /**
     * Scope for facility assets
     */
    public function scopeFacility($query)
    {
        return $query->where('asset_type', '2');
    }
}
```

### 4. WovenWorkOrder Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class WovenWorkOrder extends Model
{
    protected $table = 'woven_workorders';

    protected $fillable = [
        'woven_api_id',
        'asset_id',
        'location_id',
        'work_order_number',
        'work_order_type',
        'work_order_status',
        'work_order_status_display',
        'work_order_priority',
        'work_order_priority_display',
        'assigned_employee_id',
        'closed_by_employee_id',
        'created_on',
        'closed_date',
        'is_deleted',
        'hours_logged'
    ];

    protected $casts = [
        'created_on' => 'datetime',
        'closed_date' => 'datetime',
        'is_deleted' => 'boolean',
        'hours_logged' => 'decimal:2'
    ];

    /**
     * Get the asset this work order belongs to
     */
    public function asset(): BelongsTo
    {
        return $this->belongsTo(WovenAsset::class, 'asset_id', 'woven_api_id');
    }

    /**
     * Get assigned employee
     */
    public function assignedEmployee(): BelongsTo
    {
        return $this->belongsTo(WovenEmployee::class, 'assigned_employee_id', 'woven_api_id');
    }

    /**
     * Get employee who closed the work order
     */
    public function closedByEmployee(): BelongsTo
    {
        return $this->belongsTo(WovenEmployee::class, 'closed_by_employee_id', 'woven_api_id');
    }

    /**
     * Get club through location mapping
     */
    public function club()
    {
        return $this->belongsToMany(Club::class, 'abc_tags', 'woven_location_id', 'club_id', 'location_id', 'id');
    }

    /**
     * Scope for open work orders
     */
    public function scopeOpen($query)
    {
        return $query->where('work_order_status', '!=', '2')
                    ->where('is_deleted', 0);
    }

    /**
     * Scope for closed work orders
     */
    public function scopeClosed($query)
    {
        return $query->where('work_order_status', '2')
                    ->where('is_deleted', 0);
    }

    /**
     * Scope for asset work orders
     */
    public function scopeAssetType($query)
    {
        return $query->where('work_order_type', '1');
    }

    /**
     * Scope for facility work orders
     */
    public function scopeFacilityType($query)
    {
        return $query->where('work_order_type', '2');
    }

    /**
     * Get days since work order was created
     */
    public function getDaysOpenAttribute()
    {
        if (!$this->created_on) {
            return 0;
        }

        $endDate = $this->closed_date ?? now();
        return $this->created_on->diffInDays($endDate);
    }

    /**
     * Check if work order is overdue based on priority
     */
    public function getIsOverdueAttribute()
    {
        if ($this->work_order_status == '2') {
            return false; // Closed work orders are not overdue
        }

        $daysOpen = $this->days_open;

        // Define SLA based on priority (customize as needed)
        $slaThresholds = [
            1 => 1,  // Critical: 1 day
            2 => 3,  // High: 3 days
            3 => 7,  // Medium: 7 days
            4 => 14, // Low: 14 days
        ];

        $threshold = $slaThresholds[$this->work_order_priority] ?? 7;
        return $daysOpen > $threshold;
    }
}
```

### 5. WovenEmployee Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WovenEmployee extends Model
{
    protected $table = 'woven_employees';

    protected $fillable = [
        'woven_api_id',
        'first_name',
        'last_name',
        'email',
        'status',
        'company_id',
        'role_name',
        'position_name',
        'hire_date',
        'primary_location_id'
    ];

    protected $casts = [
        'hire_date' => 'datetime',
        'status' => 'boolean'
    ];

    /**
     * Get work orders assigned to this employee
     */
    public function assignedWorkOrders(): HasMany
    {
        return $this->hasMany(WovenWorkOrder::class, 'assigned_employee_id', 'woven_api_id');
    }

    /**
     * Get work orders closed by this employee
     */
    public function closedWorkOrders(): HasMany
    {
        return $this->hasMany(WovenWorkOrder::class, 'closed_by_employee_id', 'woven_api_id');
    }

    /**
     * Get full name attribute
     */
    public function getFullNameAttribute()
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Scope for active employees
     */
    public function scopeActive($query)
    {
        return $query->where('status', 1);
    }
}
```

### 6. CompanySetting Model
```php
<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CompanySetting extends Model
{
    protected $table = 'company_settings';

    protected $fillable = [
        'setting_key',
        'setting_value',
        'description'
    ];

    /**
     * Get setting value by key
     */
    public static function getValue($key, $default = null)
    {
        $setting = static::where('setting_key', $key)->first();
        return $setting ? $setting->setting_value : $default;
    }

    /**
     * Set setting value
     */
    public static function setValue($key, $value, $description = null)
    {
        return static::updateOrCreate(
            ['setting_key' => $key],
            [
                'setting_value' => $value,
                'description' => $description
            ]
        );
    }

    /**
     * Get facility dashboard thresholds
     */
    public static function getFacilityThresholds()
    {
        return [
            'FacEqupWorking' => (float) static::getValue('FacEqupWorking', 0.95),
            'FacEqupWorkingMid' => (float) static::getValue('FacEqupWorkingMid', 0.85),
            'FacOpenWO' => (int) static::getValue('FacOpenWO', 5),
            'FacOpenWOMid' => (int) static::getValue('FacOpenWOMid', 10),
            'FacAging' => (int) static::getValue('FacAging', 3),
            'FacAgingMid' => (int) static::getValue('FacAgingMid', 7)
        ];
    }
}
```

## Service Layer

### FacilityDashboardService
```php
<?php

namespace App\Services;

use App\Models\Club;
use App\Models\AbcTag;
use App\Models\WovenAsset;
use App\Models\WovenWorkOrder;
use App\Models\CompanySetting;
use Illuminate\Support\Collection;
use Carbon\Carbon;

class FacilityDashboardService
{
    /**
     * Get dashboard data for all clubs or specific club
     */
    public function getDashboardData($clubId = 'all', $from = null, $to = null): array
    {
        $from = $from ?? now()->startOfDay();
        $to = $to ?? now()->endOfDay();

        $query = Club::with(['tags', 'assets', 'workOrders']);

        if ($clubId !== 'all') {
            $query->where('id', $clubId);
        }

        $clubs = $query->get();
        $thresholds = CompanySetting::getFacilityThresholds();

        $data = [];
        $totals = [
            'total_assets' => 0,
            'total_open_workorders' => 0,
            'total_uptime_percentage' => 0,
            'total_avg_days_open' => 0
        ];

        foreach ($clubs as $club) {
            $clubData = $this->getClubDashboardData($club, $from, $to, $thresholds);

            // Group by district/market
            $district = $club->districtTag()?->tag_value ?? 'Unknown';

            if (!isset($data[$district])) {
                $data[$district] = [
                    'district' => $district,
                    'clubs' => [],
                    'summary' => [
                        'total_assets' => 0,
                        'total_open_workorders' => 0,
                        'avg_uptime_percentage' => 0,
                        'avg_days_open' => 0
                    ]
                ];
            }

            $data[$district]['clubs'][] = $clubData;

            // Update district totals
            $data[$district]['summary']['total_assets'] += $clubData['assets_count'];
            $data[$district]['summary']['total_open_workorders'] += $clubData['open_workorders_count'];

            // Update global totals
            $totals['total_assets'] += $clubData['assets_count'];
            $totals['total_open_workorders'] += $clubData['open_workorders_count'];
        }

        // Calculate averages for districts
        foreach ($data as $district => &$districtData) {
            $clubCount = count($districtData['clubs']);
            if ($clubCount > 0) {
                $totalUptime = array_sum(array_column($districtData['clubs'], 'uptime_percentage'));
                $totalDaysOpen = array_sum(array_column($districtData['clubs'], 'avg_days_open'));

                $districtData['summary']['avg_uptime_percentage'] = $totalUptime / $clubCount;
                $districtData['summary']['avg_days_open'] = $totalDaysOpen / $clubCount;
            }
        }

        // Calculate global averages
        $totalClubs = $clubs->count();
        if ($totalClubs > 0) {
            $totals['total_uptime_percentage'] = $clubs->avg(function ($club) {
                return $this->calculateUptimePercentage($club);
            });

            $totals['total_avg_days_open'] = $clubs->avg(function ($club) {
                return $this->calculateAverageDaysOpen($club);
            });
        }

        return [
            'data' => $data,
            'totals' => $totals,
            'thresholds' => $thresholds,
            'last_sync' => $this->getLastSyncTime()
        ];
    }

    /**
     * Get dashboard data for a specific club
     */
    private function getClubDashboardData(Club $club, $from, $to, array $thresholds): array
    {
        $equipmentAssets = $club->equipmentAssets;
        $openWorkOrders = $club->openWorkOrders;

        $assetsWithOpenWO = $equipmentAssets->filter(function ($asset) {
            return $asset->openWorkOrders()->exists();
        });

        $uptimePercentage = $this->calculateUptimePercentage($club);
        $avgDaysOpen = $this->calculateAverageDaysOpen($club);

        // Get daily counts
        $openToday = $club->workOrders()
            ->whereDate('created_on', $from->toDateString())
            ->open()
            ->count();

        $closedToday = $club->workOrders()
            ->whereDate('closed_date', $from->toDateString())
            ->closed()
            ->count();

        return [
            'club_id' => $club->id,
            'club_number' => $club->clubNumber,
            'club_name' => $club->name,
            'market' => $club->marketTag()?->tag_value,
            'region' => $club->regionTag()?->tag_value,
            'district' => $club->districtTag()?->tag_value,
            'assets_count' => $equipmentAssets->count(),
            'assets_with_open_wo' => $assetsWithOpenWO->count(),
            'open_workorders_count' => $openWorkOrders->count(),
            'uptime_percentage' => $uptimePercentage,
            'avg_days_open' => $avgDaysOpen,
            'open_today' => $openToday,
            'closed_today' => $closedToday,
            'colors' => [
                'uptime' => $this->getUptimeColor($uptimePercentage, $thresholds),
                'open_wo' => $this->getOpenWorkOrderColor($openWorkOrders->count(), $thresholds),
                'aging' => $this->getAgingColor($avgDaysOpen, $thresholds)
            ]
        ];
    }

    /**
     * Calculate uptime percentage for a club
     */
    private function calculateUptimePercentage(Club $club): float
    {
        $equipmentAssets = $club->equipmentAssets;
        $totalAssets = $equipmentAssets->count();

        if ($totalAssets === 0) {
            return 100.0;
        }

        $assetsWithOpenWO = $equipmentAssets->filter(function ($asset) {
            return $asset->openWorkOrders()->exists();
        })->count();

        return (1 - ($assetsWithOpenWO / $totalAssets)) * 100;
    }

    /**
     * Calculate average days open for work orders
     */
    private function calculateAverageDaysOpen(Club $club): float
    {
        $openWorkOrders = $club->openWorkOrders;

        if ($openWorkOrders->isEmpty()) {
            return 0.0;
        }

        $totalDays = $openWorkOrders->sum(function ($workOrder) {
            return $workOrder->days_open;
        });

        return $totalDays / $openWorkOrders->count();
    }

    /**
     * Get color coding for uptime percentage
     */
    private function getUptimeColor(float $uptimePercentage, array $thresholds): string
    {
        if ($uptimePercentage >= $thresholds['FacEqupWorking'] * 100) {
            return '#00ff27'; // Green
        } elseif ($uptimePercentage >= $thresholds['FacEqupWorkingMid'] * 100) {
            return '#ffff00'; // Yellow
        } else {
            return '#ff4136'; // Red
        }
    }

    /**
     * Get color coding for open work orders count
     */
    private function getOpenWorkOrderColor(int $openWOCount, array $thresholds): string
    {
        if ($openWOCount <= $thresholds['FacOpenWO']) {
            return '#00ff27'; // Green
        } elseif ($openWOCount <= $thresholds['FacOpenWOMid']) {
            return '#ffff00'; // Yellow
        } else {
            return '#ff4136'; // Red
        }
    }

    /**
     * Get color coding for aging (days open)
     */
    private function getAgingColor(float $avgDaysOpen, array $thresholds): string
    {
        if ($avgDaysOpen <= $thresholds['FacAging']) {
            return '#00ff27'; // Green
        } elseif ($avgDaysOpen <= $thresholds['FacAgingMid']) {
            return '#ffff00'; // Yellow
        } else {
            return '#ff4136'; // Red
        }
    }

    /**
     * Get last sync time from Woven API
     */
    private function getLastSyncTime(): ?Carbon
    {
        // This would typically come from a sync log table
        // For now, return the latest updated_at from any Woven table
        $lastAssetSync = WovenAsset::max('updated_at');
        $lastWorkOrderSync = WovenWorkOrder::max('updated_at');

        $latest = max($lastAssetSync, $lastWorkOrderSync);
        return $latest ? Carbon::parse($latest) : null;
    }

    /**
     * Get work order statistics for a date range
     */
    public function getWorkOrderStats($clubId = 'all', Carbon $date = null): array
    {
        $date = $date ?? now();
        $yesterday = $date->copy()->subDay();

        $query = WovenWorkOrder::query();

        if ($clubId !== 'all') {
            $query->whereHas('club', function ($q) use ($clubId) {
                $q->where('id', $clubId);
            });
        }

        return [
            'open_today' => (clone $query)->whereDate('created_on', $date)->open()->count(),
            'closed_today' => (clone $query)->whereDate('closed_date', $date)->closed()->count(),
            'open_yesterday' => (clone $query)->whereDate('created_on', $yesterday)->open()->count(),
            'closed_yesterday' => (clone $query)->whereDate('closed_date', $yesterday)->closed()->count(),
        ];
    }

    /**
     * Get equipment breakdown by category
     */
    public function getEquipmentBreakdown($clubId = 'all'): Collection
    {
        $query = WovenAsset::equipment();

        if ($clubId !== 'all') {
            $query->whereHas('club', function ($q) use ($clubId) {
                $q->where('id', $clubId);
            });
        }

        return $query->selectRaw('asset_category, COUNT(*) as count')
            ->groupBy('asset_category')
            ->get();
    }

    /**
     * Get work order trends over time
     */
    public function getWorkOrderTrends($clubId = 'all', int $days = 30): Collection
    {
        $startDate = now()->subDays($days);

        $query = WovenWorkOrder::where('created_on', '>=', $startDate);

        if ($clubId !== 'all') {
            $query->whereHas('club', function ($q) use ($clubId) {
                $q->where('id', $clubId);
            });
        }

        return $query->selectRaw('DATE(created_on) as date, COUNT(*) as count')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }
}
```

## Controller

### FacilityDashboardController
```php
<?php

namespace App\Http\Controllers;

use App\Services\FacilityDashboardService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class FacilityDashboardController extends Controller
{
    private FacilityDashboardService $facilityService;

    public function __construct(FacilityDashboardService $facilityService)
    {
        $this->facilityService = $facilityService;
    }

    /**
     * Display the facility dashboard
     */
    public function index(Request $request)
    {
        $clubId = $request->get('club', 'all');
        $from = $request->get('from') ? Carbon::parse($request->get('from')) : now()->startOfDay();
        $to = $request->get('to') ? Carbon::parse($request->get('to')) : now()->endOfDay();

        $dashboardData = $this->facilityService->getDashboardData($clubId, $from, $to);

        return view('facility.dashboard', [
            'data' => $dashboardData['data'],
            'totals' => $dashboardData['totals'],
            'thresholds' => $dashboardData['thresholds'],
            'lastSync' => $dashboardData['last_sync'],
            'selectedClub' => $clubId,
            'dateFrom' => $from->format('Y-m-d'),
            'dateTo' => $to->format('Y-m-d')
        ]);
    }

    /**
     * Get dashboard data as JSON (for AJAX requests)
     */
    public function getData(Request $request): JsonResponse
    {
        $clubId = $request->get('club', 'all');
        $from = $request->get('from') ? Carbon::parse($request->get('from')) : now()->startOfDay();
        $to = $request->get('to') ? Carbon::parse($request->get('to')) : now()->endOfDay();

        $data = $this->facilityService->getDashboardData($clubId, $from, $to);

        return response()->json($data);
    }

    /**
     * Get work order statistics
     */
    public function getWorkOrderStats(Request $request): JsonResponse
    {
        $clubId = $request->get('club', 'all');
        $date = $request->get('date') ? Carbon::parse($request->get('date')) : now();

        $stats = $this->facilityService->getWorkOrderStats($clubId, $date);

        return response()->json($stats);
    }

    /**
     * Get equipment breakdown
     */
    public function getEquipmentBreakdown(Request $request): JsonResponse
    {
        $clubId = $request->get('club', 'all');
        $breakdown = $this->facilityService->getEquipmentBreakdown($clubId);

        return response()->json($breakdown);
    }

    /**
     * Get work order trends
     */
    public function getWorkOrderTrends(Request $request): JsonResponse
    {
        $clubId = $request->get('club', 'all');
        $days = $request->get('days', 30);

        $trends = $this->facilityService->getWorkOrderTrends($clubId, $days);

        return response()->json($trends);
    }

    /**
     * Export dashboard data to CSV
     */
    public function exportCsv(Request $request)
    {
        $clubId = $request->get('club', 'all');
        $from = $request->get('from') ? Carbon::parse($request->get('from')) : now()->startOfDay();
        $to = $request->get('to') ? Carbon::parse($request->get('to')) : now()->endOfDay();

        $data = $this->facilityService->getDashboardData($clubId, $from, $to);

        $filename = 'facility_dashboard_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"$filename\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // CSV headers
            fputcsv($file, [
                'District', 'Club Number', 'Club Name', 'Market', 'Region',
                'Total Assets', 'Assets with Open WO', 'Open Work Orders',
                'Uptime %', 'Avg Days Open', 'Open Today', 'Closed Today'
            ]);

            // CSV data
            foreach ($data['data'] as $district => $districtData) {
                foreach ($districtData['clubs'] as $club) {
                    fputcsv($file, [
                        $district,
                        $club['club_number'],
                        $club['club_name'],
                        $club['market'],
                        $club['region'],
                        $club['assets_count'],
                        $club['assets_with_open_wo'],
                        $club['open_workorders_count'],
                        round($club['uptime_percentage'], 2),
                        round($club['avg_days_open'], 1),
                        $club['open_today'],
                        $club['closed_today']
                    ]);
                }
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }
}
```

## Routes

### web.php
```php
<?php

use App\Http\Controllers\FacilityDashboardController;

// Facility Dashboard Routes
Route::prefix('facility')->name('facility.')->group(function () {
    Route::get('/dashboard', [FacilityDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/data', [FacilityDashboardController::class, 'getData'])->name('dashboard.data');
    Route::get('/dashboard/workorder-stats', [FacilityDashboardController::class, 'getWorkOrderStats'])->name('dashboard.workorder-stats');
    Route::get('/dashboard/equipment-breakdown', [FacilityDashboardController::class, 'getEquipmentBreakdown'])->name('dashboard.equipment-breakdown');
    Route::get('/dashboard/workorder-trends', [FacilityDashboardController::class, 'getWorkOrderTrends'])->name('dashboard.workorder-trends');
    Route::get('/dashboard/export', [FacilityDashboardController::class, 'exportCsv'])->name('dashboard.export');
});
```

## Views

### resources/views/facility/dashboard.blade.php
```blade
@extends('layouts.app')

@section('title', 'Facility Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Facility District Dashboard</h3>
                    <div class="card-tools">
                        <form method="GET" class="form-inline">
                            <div class="form-group mr-2">
                                <label for="club" class="sr-only">Club</label>
                                <select name="club" id="club" class="form-control">
                                    <option value="all" {{ $selectedClub == 'all' ? 'selected' : '' }}>All Clubs</option>
                                    @foreach(\App\Models\Club::all() as $club)
                                        <option value="{{ $club->id }}" {{ $selectedClub == $club->id ? 'selected' : '' }}>
                                            {{ $club->clubNumber }} - {{ $club->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="form-group mr-2">
                                <label for="from" class="sr-only">From Date</label>
                                <input type="date" name="from" id="from" class="form-control" value="{{ $dateFrom }}">
                            </div>
                            <div class="form-group mr-2">
                                <label for="to" class="sr-only">To Date</label>
                                <input type="date" name="to" id="to" class="form-control" value="{{ $dateTo }}">
                            </div>
                            <button type="submit" class="btn btn-primary mr-2">Filter</button>
                            <a href="{{ route('facility.dashboard.export', request()->query()) }}" class="btn btn-success">Export CSV</a>
                        </form>
                    </div>
                </div>

                <div class="card-body">
                    @if($lastSync)
                        <div class="alert alert-info">
                            <i class="fas fa-sync"></i> Last sync: {{ $lastSync->format('M j, Y g:i A') }}
                        </div>
                    @endif

                    <!-- Summary Cards -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <h4>{{ number_format($totals['total_assets']) }}</h4>
                                    <p>Total Assets</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <h4>{{ number_format($totals['total_open_workorders']) }}</h4>
                                    <p>Open Work Orders</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <h4>{{ number_format($totals['total_uptime_percentage'], 1) }}%</h4>
                                    <p>Average Uptime</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <h4>{{ number_format($totals['total_avg_days_open'], 1) }}</h4>
                                    <p>Avg Days Open</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- District Tables -->
                    @foreach($data as $district => $districtData)
                        <div class="mb-4">
                            <h4>{{ $district }}</h4>
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead class="thead-dark">
                                        <tr>
                                            <th>Club #</th>
                                            <th>Club Name</th>
                                            <th>Market</th>
                                            <th>Region</th>
                                            <th>Assets</th>
                                            <th>Open WO</th>
                                            <th>Uptime %</th>
                                            <th>Avg Days Open</th>
                                            <th>Opened Today</th>
                                            <th>Closed Today</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($districtData['clubs'] as $club)
                                            <tr>
                                                <td>{{ $club['club_number'] }}</td>
                                                <td>{{ $club['club_name'] }}</td>
                                                <td>{{ $club['market'] ?? 'N/A' }}</td>
                                                <td>{{ $club['region'] ?? 'N/A' }}</td>
                                                <td>{{ $club['assets_count'] }}</td>
                                                <td>{{ $club['open_workorders_count'] }}</td>
                                                <td style="background-color: {{ $club['colors']['uptime'] }};">
                                                    {{ number_format($club['uptime_percentage'], 1) }}%
                                                </td>
                                                <td style="background-color: {{ $club['colors']['aging'] }};">
                                                    {{ number_format($club['avg_days_open'], 1) }}
                                                </td>
                                                <td>{{ $club['open_today'] }}</td>
                                                <td>{{ $club['closed_today'] }}</td>
                                            </tr>
                                        @endforeach
                                        <!-- District Summary Row -->
                                        <tr class="table-secondary font-weight-bold">
                                            <td colspan="4">{{ $district }} Summary</td>
                                            <td>{{ $districtData['summary']['total_assets'] }}</td>
                                            <td>{{ $districtData['summary']['total_open_workorders'] }}</td>
                                            <td>{{ number_format($districtData['summary']['avg_uptime_percentage'], 1) }}%</td>
                                            <td>{{ number_format($districtData['summary']['avg_days_open'], 1) }}</td>
                                            <td colspan="2">-</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    @endforeach

                    <!-- Charts Section -->
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Equipment Breakdown</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="equipmentChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Work Order Trends (Last 30 Days)</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="trendsChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Load equipment breakdown chart
    fetch('{{ route("facility.dashboard.equipment-breakdown") }}?' + new URLSearchParams({
        club: '{{ $selectedClub }}'
    }))
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('equipmentChart').getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: data.map(item => item.asset_category || 'Unknown'),
                datasets: [{
                    data: data.map(item => item.count),
                    backgroundColor: [
                        '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                        '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false
            }
        });
    });

    // Load work order trends chart
    fetch('{{ route("facility.dashboard.workorder-trends") }}?' + new URLSearchParams({
        club: '{{ $selectedClub }}',
        days: 30
    }))
    .then(response => response.json())
    .then(data => {
        const ctx = document.getElementById('trendsChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: data.map(item => item.date),
                datasets: [{
                    label: 'Work Orders Created',
                    data: data.map(item => item.count),
                    borderColor: '#36A2EB',
                    backgroundColor: 'rgba(54, 162, 235, 0.1)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });

    // Auto-refresh every 5 minutes
    setInterval(function() {
        location.reload();
    }, 300000);
});
</script>
@endpush
```

## Database Migrations

### Create abc_tags Migration
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::create('abc_tags', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('club_id');
            $table->string('tag_type', 50); // 'market', 'region', 'district'
            $table->string('tag_value', 100); // 'Market 1', 'Market 2', etc.
            $table->string('woven_location_id', 36)->nullable(); // Maps to Woven API
            $table->timestamps();

            $table->foreign('club_id')->references('id')->on('abc_clubs');
            $table->index(['club_id', 'tag_type']);
            $table->index('woven_location_id');
        });
    }

    public function down()
    {
        Schema::dropIfExists('abc_tags');
    }
};
```

### Update Woven Tables Migration
```php
<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        // Add indexes for better performance
        Schema::table('woven_assets', function (Blueprint $table) {
            $table->index(['location_id', 'asset_type']);
            $table->index('asset_type');
        });

        Schema::table('woven_workorders', function (Blueprint $table) {
            $table->index(['location_id', 'work_order_status']);
            $table->index(['work_order_status', 'is_deleted']);
            $table->index('created_on');
            $table->index('closed_date');
        });

        Schema::table('woven_employees', function (Blueprint $table) {
            $table->index('primary_location_id');
            $table->index('status');
        });
    }

    public function down()
    {
        Schema::table('woven_assets', function (Blueprint $table) {
            $table->dropIndex(['location_id', 'asset_type']);
            $table->dropIndex(['asset_type']);
        });

        Schema::table('woven_workorders', function (Blueprint $table) {
            $table->dropIndex(['location_id', 'work_order_status']);
            $table->dropIndex(['work_order_status', 'is_deleted']);
            $table->dropIndex(['created_on']);
            $table->dropIndex(['closed_date']);
        });

        Schema::table('woven_employees', function (Blueprint $table) {
            $table->dropIndex(['primary_location_id']);
            $table->dropIndex(['status']);
        });
    }
};
```

## Seeders

### CompanySettingsSeeder
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\CompanySetting;

class CompanySettingsSeeder extends Seeder
{
    public function run()
    {
        $settings = [
            [
                'setting_key' => 'FacEqupWorking',
                'setting_value' => '0.95',
                'description' => 'Equipment working percentage threshold (high) - 95%'
            ],
            [
                'setting_key' => 'FacEqupWorkingMid',
                'setting_value' => '0.85',
                'description' => 'Equipment working percentage threshold (medium) - 85%'
            ],
            [
                'setting_key' => 'FacOpenWO',
                'setting_value' => '5',
                'description' => 'Open work orders threshold (low is good) - 5 or fewer'
            ],
            [
                'setting_key' => 'FacOpenWOMid',
                'setting_value' => '10',
                'description' => 'Open work orders threshold (medium) - 10 or fewer'
            ],
            [
                'setting_key' => 'FacAging',
                'setting_value' => '3',
                'description' => 'Work order aging threshold in days (low is good) - 3 days or less'
            ],
            [
                'setting_key' => 'FacAgingMid',
                'setting_value' => '7',
                'description' => 'Work order aging threshold in days (medium) - 7 days or less'
            ]
        ];

        foreach ($settings as $setting) {
            CompanySetting::updateOrCreate(
                ['setting_key' => $setting['setting_key']],
                $setting
            );
        }
    }
}
```

### AbcTagsSeeder (Example)
```php
<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Club;
use App\Models\AbcTag;

class AbcTagsSeeder extends Seeder
{
    public function run()
    {
        // Example data - replace with your actual club/location mappings
        $clubMappings = [
            1 => [
                'market' => 'Market 1',
                'region' => 'Region A',
                'district' => 'District North',
                'woven_location_id' => 'woven-location-uuid-1'
            ],
            2 => [
                'market' => 'Market 1',
                'region' => 'Region A',
                'district' => 'District North',
                'woven_location_id' => 'woven-location-uuid-2'
            ],
            3 => [
                'market' => 'Market 2',
                'region' => 'Region B',
                'district' => 'District South',
                'woven_location_id' => 'woven-location-uuid-3'
            ]
        ];

        foreach ($clubMappings as $clubId => $mapping) {
            foreach (['market', 'region', 'district'] as $tagType) {
                AbcTag::updateOrCreate(
                    [
                        'club_id' => $clubId,
                        'tag_type' => $tagType
                    ],
                    [
                        'tag_value' => $mapping[$tagType],
                        'woven_location_id' => $mapping['woven_location_id']
                    ]
                );
            }
        }
    }
}
```

## API Resources (Optional)

### FacilityDashboardResource
```php
<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class FacilityDashboardResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'club_id' => $this['club_id'],
            'club_number' => $this['club_number'],
            'club_name' => $this['club_name'],
            'market' => $this['market'],
            'region' => $this['region'],
            'district' => $this['district'],
            'metrics' => [
                'assets_count' => $this['assets_count'],
                'assets_with_open_wo' => $this['assets_with_open_wo'],
                'open_workorders_count' => $this['open_workorders_count'],
                'uptime_percentage' => round($this['uptime_percentage'], 2),
                'avg_days_open' => round($this['avg_days_open'], 1)
            ],
            'daily_stats' => [
                'open_today' => $this['open_today'],
                'closed_today' => $this['closed_today']
            ],
            'colors' => $this['colors'],
            'status_indicators' => [
                'uptime_status' => $this->getStatusFromColor($this['colors']['uptime']),
                'workorder_status' => $this->getStatusFromColor($this['colors']['open_wo']),
                'aging_status' => $this->getStatusFromColor($this['colors']['aging'])
            ]
        ];
    }

    private function getStatusFromColor($color)
    {
        return match($color) {
            '#00ff27' => 'good',
            '#ffff00' => 'warning',
            '#ff4136' => 'critical',
            default => 'unknown'
        };
    }
}
```

## Testing

### Feature Test Example
```php
<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Club;
use App\Models\AbcTag;
use App\Models\WovenAsset;
use App\Models\WovenWorkOrder;
use App\Models\CompanySetting;
use Illuminate\Foundation\Testing\RefreshDatabase;

class FacilityDashboardTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Seed company settings
        $this->artisan('db:seed', ['--class' => 'CompanySettingsSeeder']);
    }

    public function test_facility_dashboard_displays_correctly()
    {
        // Create test data
        $club = Club::factory()->create(['clubNumber' => 101, 'name' => 'Test Club']);

        AbcTag::create([
            'club_id' => $club->id,
            'tag_type' => 'market',
            'tag_value' => 'Market 1',
            'woven_location_id' => 'test-location-id'
        ]);

        $asset = WovenAsset::factory()->create([
            'location_id' => 'test-location-id',
            'asset_type' => '1'
        ]);

        WovenWorkOrder::factory()->create([
            'asset_id' => $asset->woven_api_id,
            'location_id' => 'test-location-id',
            'work_order_status' => '1', // Open
            'is_deleted' => 0
        ]);

        $response = $this->get('/facility/dashboard');

        $response->assertStatus(200);
        $response->assertSee('Test Club');
        $response->assertSee('Market 1');
    }

    public function test_dashboard_api_returns_correct_data()
    {
        // Similar setup as above
        $club = Club::factory()->create();

        $response = $this->getJson('/facility/dashboard/data');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'data',
            'totals',
            'thresholds',
            'last_sync'
        ]);
    }
}
```

## Configuration & Environment

### Key Configuration Points

#### 1. Woven API Configuration
```php
// config/woven.php
return [
    'api_url' => env('WOVEN_API_URL', 'https://gateway-api.woven.team'),
    'client_id' => env('WOVEN_CLIENT_ID'),
    'client_secret' => env('WOVEN_CLIENT_SECRET'),
    'timeout' => env('WOVEN_API_TIMEOUT', 30),
    'retry_attempts' => env('WOVEN_API_RETRY_ATTEMPTS', 3),
];
```

#### 2. Environment Variables
```env
# Woven API Configuration
WOVEN_API_URL=https://gateway-api.woven.team
WOVEN_CLIENT_ID=your_client_id
WOVEN_CLIENT_SECRET=your_client_secret
WOVEN_API_TIMEOUT=30
WOVEN_API_RETRY_ATTEMPTS=3

# Dashboard Configuration
FACILITY_DASHBOARD_REFRESH_INTERVAL=300000
FACILITY_DASHBOARD_CACHE_TTL=300
```

## Performance Optimization

### 1. Database Indexing Strategy
```sql
-- Critical indexes for facility dashboard performance
CREATE INDEX idx_woven_assets_location_type ON woven_assets(location_id, asset_type);
CREATE INDEX idx_woven_workorders_status_deleted ON woven_workorders(work_order_status, is_deleted);
CREATE INDEX idx_woven_workorders_location_status ON woven_workorders(location_id, work_order_status);
CREATE INDEX idx_abc_tags_club_type ON abc_tags(club_id, tag_type);
CREATE INDEX idx_abc_tags_woven_location ON abc_tags(woven_location_id);
```

### 2. Caching Strategy
```php
// In FacilityDashboardService
use Illuminate\Support\Facades\Cache;

public function getDashboardData($clubId = 'all', $from = null, $to = null): array
{
    $cacheKey = "facility_dashboard_{$clubId}_" . md5($from . $to);

    return Cache::remember($cacheKey, 300, function () use ($clubId, $from, $to) {
        // Expensive dashboard calculation logic here
        return $this->calculateDashboardData($clubId, $from, $to);
    });
}
```

### 3. Query Optimization
```php
// Eager loading to prevent N+1 queries
$clubs = Club::with([
    'tags' => function ($query) {
        $query->whereIn('tag_type', ['market', 'region', 'district']);
    },
    'assets' => function ($query) {
        $query->where('asset_type', '1')
              ->with(['openWorkOrders']);
    }
])->get();
```

## Deployment Checklist

### 1. Database Setup
- [ ] Run migrations for `abc_tags` table
- [ ] Add performance indexes
- [ ] Seed company settings
- [ ] Migrate existing club/location data to `abc_tags`

### 2. Woven API Integration
- [ ] Verify Woven API credentials
- [ ] Test API connectivity
- [ ] Confirm data sync is working
- [ ] Validate location mapping in `abc_tags`

### 3. Dashboard Configuration
- [ ] Set facility threshold values
- [ ] Configure caching
- [ ] Test dashboard performance
- [ ] Verify color coding logic

### 4. Security & Access
- [ ] Implement authentication middleware
- [ ] Set up role-based access control
- [ ] Configure CSRF protection
- [ ] Validate input sanitization

## Troubleshooting Guide

### Common Issues

#### 1. Missing Location Mapping
**Problem**: Clubs not showing data despite having assets/work orders
**Solution**: Verify `abc_tags` table has correct `woven_location_id` mappings

```sql
-- Check for clubs without location mapping
SELECT c.id, c.name, c.clubNumber
FROM abc_clubs c
LEFT JOIN abc_tags t ON c.id = t.club_id AND t.woven_location_id IS NOT NULL
WHERE t.id IS NULL;
```

#### 2. Performance Issues
**Problem**: Dashboard loading slowly
**Solutions**:
- Check database indexes are in place
- Enable query caching
- Optimize eager loading relationships
- Consider database query optimization

#### 3. Incorrect Color Coding
**Problem**: Colors not matching expected thresholds
**Solution**: Verify company settings values and calculation logic

```php
// Debug color calculation
$thresholds = CompanySetting::getFacilityThresholds();
dd($thresholds); // Check threshold values

// Test color calculation
$uptimePercentage = 92.5;
$color = $this->getUptimeColor($uptimePercentage, $thresholds);
```

## Migration from CodeIgniter

### Data Migration Steps

#### 1. Club Settings to Tags Migration
```php
// Migration script to convert abc_club_settings to abc_tags
use App\Models\Club;
use App\Models\AbcTag;
use Illuminate\Support\Facades\DB;

// Get existing club settings (from CodeIgniter database)
$clubSettings = DB::connection('codeigniter')->table('abc_club_settings')
    ->select('club_id', 'MaintainDistrict', 'Region', 'wovenid')
    ->get();

foreach ($clubSettings as $setting) {
    // Create market tag (if you have market data)
    AbcTag::updateOrCreate([
        'club_id' => $setting->club_id,
        'tag_type' => 'market'
    ], [
        'tag_value' => $this->determineMarketFromDistrict($setting->MaintainDistrict),
        'woven_location_id' => $setting->wovenid
    ]);

    // Create region tag
    AbcTag::updateOrCreate([
        'club_id' => $setting->club_id,
        'tag_type' => 'region'
    ], [
        'tag_value' => $setting->Region,
        'woven_location_id' => $setting->wovenid
    ]);

    // Create district tag
    AbcTag::updateOrCreate([
        'club_id' => $setting->club_id,
        'tag_type' => 'district'
    ], [
        'tag_value' => $setting->MaintainDistrict,
        'woven_location_id' => $setting->wovenid
    ]);
}
```

#### 2. Validation Script
```php
// Validate migration success
$clubsWithoutTags = Club::whereDoesntHave('tags')->get();
if ($clubsWithoutTags->count() > 0) {
    echo "Warning: {$clubsWithoutTags->count()} clubs missing tag data\n";
}

$assetsWithoutClubs = WovenAsset::whereDoesntHave('club')->count();
if ($assetsWithoutClubs > 0) {
    echo "Warning: {$assetsWithoutClubs} assets not linked to clubs\n";
}
```

This comprehensive guide provides everything needed to implement the facility dashboard in Laravel V2 with the new `abc_tags` structure. The implementation maintains all the functionality from the original CodeIgniter version while leveraging Laravel's modern features and the new tagging system for better organization and flexibility.
